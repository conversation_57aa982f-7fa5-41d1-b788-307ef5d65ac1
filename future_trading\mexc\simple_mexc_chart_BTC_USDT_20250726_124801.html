
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📊 MEXC Live Charts - BTC_USDT</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0d1421 0%, #1a1d29 100%);
            color: #e1e3e6;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1600px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: linear-gradient(135deg, #1e2329 0%, #2b3139 100%);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 25px;
            border: 1px solid #363a45;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.4);
        }
        
        .title {
            color: #ffffff;
            font-size: 2.5em;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .live-badge {
            background: linear-gradient(45deg, #00d4aa, #00b894);
            color: #000;
            padding: 8px 16px;
            border-radius: 25px;
            font-size: 0.4em;
            font-weight: bold;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }
        
        .price-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .price-card {
            background: rgba(54, 58, 69, 0.4);
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            border: 1px solid #363a45;
            transition: transform 0.3s ease;
        }
        
        .price-card:hover {
            transform: translateY(-5px);
        }
        
        .price-value {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 8px;
        }
        
        .price-label {
            color: #868993;
            font-size: 0.9em;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .controls {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-bottom: 25px;
            flex-wrap: wrap;
        }
        
        .control-group {
            display: flex;
            gap: 5px;
            background: #2b3139;
            border-radius: 10px;
            padding: 8px;
            border: 1px solid #363a45;
        }
        
        .control-btn {
            background: transparent;
            color: #d1d4dc;
            border: none;
            padding: 12px 18px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.9em;
            transition: all 0.3s ease;
            font-weight: 600;
        }
        
        .control-btn:hover {
            background: #363a45;
            transform: translateY(-2px);
        }
        
        .control-btn.active {
            background: linear-gradient(135deg, #00d4aa 0%, #00b894 100%);
            color: #000;
            box-shadow: 0 4px 15px rgba(0, 212, 170, 0.4);
        }
        
        .chart-container {
            background: linear-gradient(135deg, #1e2329 0%, #2b3139 100%);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 25px;
            border: 1px solid #363a45;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3);
        }
        
        .chart-title {
            color: #ffffff;
            font-size: 1.5em;
            margin-bottom: 25px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .main-chart { height: 600px; }
        .indicator-chart { height: 200px; }
        
        .indicators-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin-top: 25px;
        }
        
        .indicator-card {
            background: linear-gradient(135deg, #2b3139 0%, #363a45 100%);
            border-radius: 15px;
            padding: 25px;
            border: 1px solid #4a4e5a;
            transition: transform 0.3s ease;
        }
        
        .indicator-card:hover {
            transform: translateY(-5px);
        }
        
        .indicator-name {
            color: #00d4aa;
            font-weight: bold;
            margin-bottom: 15px;
            font-size: 1em;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .indicator-value {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .indicator-description {
            color: #868993;
            font-size: 0.9em;
            line-height: 1.4;
        }
        
        .refresh-btn {
            position: fixed;
            bottom: 30px;
            right: 30px;
            background: linear-gradient(135deg, #00d4aa 0%, #00b894 100%);
            color: #000;
            border: none;
            padding: 18px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 1.4em;
            box-shadow: 0 6px 25px rgba(0, 212, 170, 0.5);
            transition: all 0.3s ease;
        }
        
        .refresh-btn:hover {
            transform: scale(1.1) rotate(180deg);
            box-shadow: 0 8px 30px rgba(0, 212, 170, 0.7);
        }
        
        @media (max-width: 768px) {
            .container { padding: 15px; }
            .title { font-size: 2em; }
            .main-chart { height: 400px; }
            .indicator-chart { height: 150px; }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="title">
                📊 BTC/USDT Live Analysis
                <span class="live-badge">LIVE MEXC DATA</span>
            </div>
            
            <div class="price-info">
                <div class="price-card">
                    <div class="price-value" style="color: #00d4aa;">$0.00</div>
                    <div class="price-label">Current Price</div>
                </div>
                <div class="price-card">
                    <div class="price-value" style="color: #00d4aa;">
                        ▲ +0.00%
                    </div>
                    <div class="price-label">24h Change</div>
                </div>
                <div class="price-card">
                    <div class="price-value" style="color: #2196f3;">0</div>
                    <div class="price-label">24h Volume</div>
                </div>
                <div class="price-card">
                    <div class="price-value" style="color: #ffeb3b;">12:48:01</div>
                    <div class="price-label">Last Update</div>
                </div>
            </div>
        </div>
        
        <!-- Controls -->
        <div class="controls">
            <div class="control-group">
                <button class="control-btn" onclick="toggleIndicator('volume')">Volume</button>
                <button class="control-btn" onclick="toggleIndicator('rsi')">RSI</button>
                <button class="control-btn" onclick="toggleIndicator('macd')">MACD</button>
            </div>
            
            <div class="control-group">
                <button class="control-btn active" onclick="toggleOverlay('ema')">EMAs</button>
                <button class="control-btn" onclick="toggleOverlay('bb')">Bollinger</button>
                <button class="control-btn" onclick="toggleOverlay('sma')">SMAs</button>
            </div>
        </div>
        
        <!-- Main Price Chart -->
        <div class="chart-container">
            <div class="chart-title">📈 Price Chart with Technical Analysis</div>
            <div id="priceChart" class="main-chart"></div>
        </div>
        
        <!-- Volume Chart -->
        <div class="chart-container" id="volumePanel" style="display: none;">
            <div class="chart-title">📊 Volume Analysis</div>
            <div id="volumeChart" class="indicator-chart"></div>
        </div>
        
        <!-- RSI Chart -->
        <div class="chart-container" id="rsiPanel" style="display: none;">
            <div class="chart-title">📈 RSI Momentum Indicator</div>
            <div id="rsiChart" class="indicator-chart"></div>
        </div>
        
        <!-- MACD Chart -->
        <div class="chart-container" id="macdPanel" style="display: none;">
            <div class="chart-title">📊 MACD Trend Analysis</div>
            <div id="macdChart" class="indicator-chart"></div>
        </div>
        
        <!-- Indicators Summary -->
        <div class="indicators-grid">
            <div class="indicator-card">
                <div class="indicator-name">RSI Momentum</div>
                <div class="indicator-value" id="rsiValue">--</div>
                <div class="indicator-description" id="rsiDesc">Relative Strength Index measures momentum</div>
            </div>
            
            <div class="indicator-card">
                <div class="indicator-name">MACD Signal</div>
                <div class="indicator-value" id="macdValue">--</div>
                <div class="indicator-description" id="macdDesc">Moving Average Convergence Divergence</div>
            </div>
            
            <div class="indicator-card">
                <div class="indicator-name">Volume Activity</div>
                <div class="indicator-value" id="volumeValue">--</div>
                <div class="indicator-description" id="volumeDesc">Volume compared to 20-period average</div>
            </div>
            
            <div class="indicator-card">
                <div class="indicator-name">Bollinger Position</div>
                <div class="indicator-value" id="bbValue">--</div>
                <div class="indicator-description" id="bbDesc">Price position within Bollinger Bands</div>
            </div>
        </div>
    </div>
    
    <!-- Refresh Button -->
    <button class="refresh-btn" onclick="refreshData()" title="Refresh Data">🔄</button>

    <script>
        // Data from Python
        const chartData = [];
        
        let visibleIndicators = new Set();
        let visibleOverlays = new Set(['ema']);
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            createPriceChart();
            updateIndicatorValues();
        });
        
        function toggleIndicator(indicator) {
            const panel = document.getElementById(indicator + 'Panel');
            if (visibleIndicators.has(indicator)) {
                visibleIndicators.delete(indicator);
                panel.style.display = 'none';
                event.target.classList.remove('active');
            } else {
                visibleIndicators.add(indicator);
                panel.style.display = 'block';
                event.target.classList.add('active');
                createIndicatorChart(indicator);
            }
        }
        
        function toggleOverlay(overlay) {
            if (visibleOverlays.has(overlay)) {
                visibleOverlays.delete(overlay);
                event.target.classList.remove('active');
            } else {
                visibleOverlays.add(overlay);
                event.target.classList.add('active');
            }
            createPriceChart();
        }
        
        function createPriceChart() {
            const data = chartData;
            const traces = [];
            
            // Candlestick
            traces.push({
                x: data.map(d => d.timestamp),
                open: data.map(d => d.open),
                high: data.map(d => d.high),
                low: data.map(d => d.low),
                close: data.map(d => d.close),
                type: 'candlestick',
                name: 'BTC/USDT',
                increasing: {fillcolor: '#00d4aa', line: {color: '#00d4aa'}},
                decreasing: {fillcolor: '#ff6b6b', line: {color: '#ff6b6b'}}
            });
            
            // EMAs
            if (visibleOverlays.has('ema')) {
                traces.push({
                    x: data.map(d => d.timestamp),
                    y: data.map(d => d.ema_20),
                    type: 'scatter',
                    mode: 'lines',
                    name: 'EMA 20',
                    line: {color: '#ffeb3b', width: 2},
                    opacity: 0.8
                });
                
                traces.push({
                    x: data.map(d => d.timestamp),
                    y: data.map(d => d.ema_50),
                    type: 'scatter',
                    mode: 'lines',
                    name: 'EMA 50',
                    line: {color: '#9c27b0', width: 2},
                    opacity: 0.8
                });
            }
            
            // SMAs
            if (visibleOverlays.has('sma')) {
                traces.push({
                    x: data.map(d => d.timestamp),
                    y: data.map(d => d.sma_20),
                    type: 'scatter',
                    mode: 'lines',
                    name: 'SMA 20',
                    line: {color: '#ff9800', width: 2},
                    opacity: 0.8
                });
                
                traces.push({
                    x: data.map(d => d.timestamp),
                    y: data.map(d => d.sma_50),
                    type: 'scatter',
                    mode: 'lines',
                    name: 'SMA 50',
                    line: {color: '#2196f3', width: 2},
                    opacity: 0.8
                });
            }
            
            // Bollinger Bands
            if (visibleOverlays.has('bb')) {
                traces.push({
                    x: data.map(d => d.timestamp),
                    y: data.map(d => d.bb_upper),
                    type: 'scatter',
                    mode: 'lines',
                    name: 'BB Upper',
                    line: {color: '#64b5f6', width: 1, dash: 'dot'},
                    opacity: 0.6
                });
                
                traces.push({
                    x: data.map(d => d.timestamp),
                    y: data.map(d => d.bb_lower),
                    type: 'scatter',
                    mode: 'lines',
                    name: 'BB Lower',
                    line: {color: '#64b5f6', width: 1, dash: 'dot'},
                    opacity: 0.6,
                    fill: 'tonexty',
                    fillcolor: 'rgba(100, 181, 246, 0.1)'
                });
            }
            
            const layout = {
                plot_bgcolor: '#0d1421',
                paper_bgcolor: '#1e2329',
                font: {color: '#e1e3e6', size: 12},
                margin: {l: 60, r: 60, t: 30, b: 40},
                xaxis: {
                    type: 'date',
                    gridcolor: '#363a45',
                    showgrid: true,
                    rangeslider: {visible: false}
                },
                yaxis: {
                    title: 'Price ($)',
                    gridcolor: '#363a45',
                    showgrid: true
                },
                showlegend: true,
                legend: {
                    x: 0,
                    y: 1,
                    bgcolor: 'rgba(30, 35, 41, 0.8)',
                    bordercolor: '#363a45',
                    borderwidth: 1
                },
                hovermode: 'x unified'
            };
            
            Plotly.newPlot('priceChart', traces, layout, {responsive: true});
        }
        
        function createIndicatorChart(indicator) {
            const data = chartData;
            const traces = [];
            let layout = {
                plot_bgcolor: '#0d1421',
                paper_bgcolor: '#1e2329',
                font: {color: '#e1e3e6', size: 11},
                margin: {l: 60, r: 60, t: 10, b: 30},
                xaxis: {
                    type: 'date',
                    gridcolor: '#363a45',
                    showgrid: true
                },
                yaxis: {
                    gridcolor: '#363a45',
                    showgrid: true
                },
                showlegend: false,
                hovermode: 'x'
            };
            
            if (indicator === 'volume') {
                traces.push({
                    x: data.map(d => d.timestamp),
                    y: data.map(d => d.volume),
                    type: 'bar',
                    name: 'Volume',
                    marker: {
                        color: data.map(d => d.close > d.open ? '#00d4aa' : '#ff6b6b'),
                        opacity: 0.7
                    }
                });
                
                traces.push({
                    x: data.map(d => d.timestamp),
                    y: data.map(d => d.volume_sma),
                    type: 'scatter',
                    mode: 'lines',
                    name: 'Volume SMA',
                    line: {color: '#ffeb3b', width: 2}
                });
                
                layout.yaxis.title = 'Volume';
            } else if (indicator === 'rsi') {
                traces.push({
                    x: data.map(d => d.timestamp),
                    y: data.map(d => d.rsi),
                    type: 'scatter',
                    mode: 'lines',
                    name: 'RSI',
                    line: {color: '#9c27b0', width: 3}
                });
                
                // RSI levels
                [70, 50, 30].forEach((level, i) => {
                    const colors = ['#ff6b6b', '#868993', '#00d4aa'];
                    traces.push({
                        x: data.map(d => d.timestamp),
                        y: Array(data.length).fill(level),
                        type: 'scatter',
                        mode: 'lines',
                        line: {color: colors[i], width: 1, dash: 'dash'},
                        opacity: 0.5
                    });
                });
                
                layout.yaxis.title = 'RSI';
                layout.yaxis.range = [0, 100];
            } else if (indicator === 'macd') {
                traces.push({
                    x: data.map(d => d.timestamp),
                    y: data.map(d => d.macd),
                    type: 'scatter',
                    mode: 'lines',
                    name: 'MACD',
                    line: {color: '#2196f3', width: 2}
                });
                traces.push({
                    x: data.map(d => d.timestamp),
                    y: data.map(d => d.macd_signal),
                    type: 'scatter',
                    mode: 'lines',
                    name: 'Signal',
                    line: {color: '#ff9800', width: 2}
                });
                traces.push({
                    x: data.map(d => d.timestamp),
                    y: data.map(d => d.macd_histogram),
                    type: 'bar',
                    name: 'Histogram',
                    marker: {
                        color: data.map(d => d.macd_histogram > 0 ? '#00d4aa' : '#ff6b6b'),
                        opacity: 0.6
                    }
                });
                layout.yaxis.title = 'MACD';
            }
            
            Plotly.newPlot(indicator + 'Chart', traces, layout, {responsive: true});
        }
        
        function updateIndicatorValues() {
            const data = chartData;
            const latest = data[data.length - 1];
            
            // RSI
            const rsi = latest.rsi || 0;
            document.getElementById('rsiValue').textContent = rsi.toFixed(1);
            document.getElementById('rsiValue').style.color = 
                rsi > 70 ? '#ff6b6b' : rsi < 30 ? '#00d4aa' : '#e1e3e6';
            document.getElementById('rsiDesc').textContent = 
                rsi > 70 ? 'Overbought - Consider selling' : 
                rsi < 30 ? 'Oversold - Consider buying' : 
                'Neutral momentum';
            
            // MACD
            const macd = latest.macd || 0;
            document.getElementById('macdValue').textContent = macd.toFixed(4);
            document.getElementById('macdValue').style.color = macd > 0 ? '#00d4aa' : '#ff6b6b';
            document.getElementById('macdDesc').textContent = 
                macd > 0 ? 'Bullish momentum' : 'Bearish momentum';
            
            // Volume
            const volumeRatio = latest.volume_ratio || 0;
            document.getElementById('volumeValue').textContent = volumeRatio.toFixed(2) + 'x';
            document.getElementById('volumeValue').style.color = 
                volumeRatio > 1.5 ? '#00d4aa' : volumeRatio < 0.5 ? '#ff6b6b' : '#e1e3e6';
            document.getElementById('volumeDesc').textContent = 
                volumeRatio > 1.5 ? 'High volume activity' : 
                volumeRatio < 0.5 ? 'Low volume activity' : 
                'Normal volume levels';
            
            // Bollinger Bands
            const price = latest.close || 0;
            const bbUpper = latest.bb_upper || 0;
            const bbLower = latest.bb_lower || 0;
            const bbPosition = ((price - bbLower) / (bbUpper - bbLower) * 100);
            
            document.getElementById('bbValue').textContent = bbPosition.toFixed(1) + '%';
            document.getElementById('bbValue').style.color = 
                bbPosition > 80 ? '#ff6b6b' : bbPosition < 20 ? '#00d4aa' : '#e1e3e6';
            document.getElementById('bbDesc').textContent = 
                bbPosition > 80 ? 'Near upper band - potential resistance' : 
                bbPosition < 20 ? 'Near lower band - potential support' : 
                'Middle range - neutral';
        }
        
        function refreshData() {
            location.reload();
        }
    </script>
</body>
</html>
