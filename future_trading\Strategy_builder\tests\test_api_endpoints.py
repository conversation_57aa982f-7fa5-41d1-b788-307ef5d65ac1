#!/usr/bin/env python3
"""
API Endpoints Test Script for Strategy Builder
Tests all REST API endpoints
"""
import requests
import json
import time
from datetime import datetime
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# API Configuration
API_BASE_URL = "http://localhost:8000/api"
TEST_SYMBOL = "BTCUSDT"
TEST_TIMEFRAME = "1h"
TEST_EXCHANGE = "binance"


class APITester:
    """API endpoint tester"""
    
    def __init__(self):
        self.session = requests.Session()
        self.session_id = None
        self.entry_mark_id = None
        self.test_results = {}
    
    def test_endpoint(self, name, method, endpoint, data=None, expected_status=200):
        """Test a single API endpoint"""
        try:
            url = f"{API_BASE_URL}{endpoint}"
            logger.info(f"Testing {method} {endpoint}")
            
            if method.upper() == 'GET':
                response = self.session.get(url, params=data)
            elif method.upper() == 'POST':
                response = self.session.post(url, json=data)
            elif method.upper() == 'DELETE':
                response = self.session.delete(url)
            else:
                raise ValueError(f"Unsupported method: {method}")
            
            success = response.status_code == expected_status
            
            if success:
                logger.info(f"✅ {name}: {response.status_code}")
                try:
                    result_data = response.json()
                    logger.debug(f"Response: {json.dumps(result_data, indent=2)[:200]}...")
                    return result_data
                except:
                    return response.text
            else:
                logger.error(f"❌ {name}: {response.status_code} - {response.text}")
                return None
                
        except Exception as e:
            logger.error(f"❌ {name}: Error - {e}")
            return None
    
    def run_all_tests(self):
        """Run all API tests"""
        logger.info("🚀 Starting API Endpoint Tests")
        logger.info("=" * 50)
        
        # Test health endpoint
        self.test_health()
        
        # Test configuration endpoints
        self.test_config()
        
        # Test OHLCV endpoints
        self.test_ohlcv_endpoints()
        
        # Test indicator endpoints
        self.test_indicator_endpoints()
        
        # Test session management
        self.test_session_management()
        
        # Test trade marking
        self.test_trade_marking()
        
        # Test strategy endpoints
        self.test_strategy_endpoints()
        
        # Print summary
        self.print_summary()
    
    def test_health(self):
        """Test health and system endpoints"""
        logger.info("\n📋 Testing Health & System Endpoints")
        logger.info("-" * 40)
        
        # Health check
        health_data = self.test_endpoint("Health Check", "GET", "/health")
        if health_data:
            logger.info(f"Status: {health_data.get('status')}")
            logger.info(f"Version: {health_data.get('version')}")
        
        # Configuration
        config_data = self.test_endpoint("Configuration", "GET", "/config")
        if config_data:
            logger.info(f"Supported timeframes: {len(config_data.get('supported_timeframes', []))}")
            logger.info(f"Supported indicators: {len(config_data.get('supported_indicators', []))}")
        
        # Exchanges
        exchanges_data = self.test_endpoint("Exchanges", "GET", "/exchanges")
        if exchanges_data:
            logger.info(f"Exchanges: {exchanges_data.get('exchanges')}")
    
    def test_config(self):
        """Test configuration endpoints"""
        logger.info("\n📋 Testing Configuration Endpoints")
        logger.info("-" * 40)
        
        # Get symbols for exchange
        symbols_data = self.test_endpoint("Symbols", "GET", f"/symbols/{TEST_EXCHANGE}")
        if symbols_data:
            logger.info(f"Available symbols: {len(symbols_data.get('symbols', []))}")
    
    def test_ohlcv_endpoints(self):
        """Test OHLCV data endpoints"""
        logger.info("\n📋 Testing OHLCV Endpoints")
        logger.info("-" * 40)
        
        # Get OHLCV data
        ohlcv_data = self.test_endpoint(
            "OHLCV Data", 
            "GET", 
            f"/ohlcv/{TEST_EXCHANGE}/{TEST_SYMBOL}/{TEST_TIMEFRAME}",
            {"limit": 100}
        )
        
        if ohlcv_data:
            logger.info(f"Received {ohlcv_data.get('count', 0)} candles")
            logger.info(f"From cache: {ohlcv_data.get('from_cache', False)}")
        
        # Get latest candle
        latest_data = self.test_endpoint(
            "Latest Candle",
            "GET",
            f"/ohlcv/{TEST_EXCHANGE}/{TEST_SYMBOL}/{TEST_TIMEFRAME}/latest"
        )
        
        if latest_data:
            logger.info(f"Latest candle timestamp: {latest_data.get('latest_candle', {}).get('timestamp')}")
        
        # Test POST endpoint
        post_data = {
            "exchange": TEST_EXCHANGE,
            "symbol": TEST_SYMBOL,
            "timeframe": TEST_TIMEFRAME,
            "limit": 50
        }
        
        post_result = self.test_endpoint("OHLCV POST", "POST", "/ohlcv/fetch", post_data)
        if post_result:
            logger.info(f"POST result: {post_result.get('count', 0)} candles")
    
    def test_indicator_endpoints(self):
        """Test technical indicator endpoints"""
        logger.info("\n📋 Testing Indicator Endpoints")
        logger.info("-" * 40)
        
        # Get supported indicators
        supported_data = self.test_endpoint("Supported Indicators", "GET", "/indicators/supported")
        if supported_data:
            indicators = supported_data.get('indicators', {})
            logger.info(f"Supported indicators: {list(indicators.keys())}")
        
        # Calculate indicators
        indicator_request = {
            "symbol": TEST_SYMBOL,
            "timeframe": TEST_TIMEFRAME,
            "indicators": [
                {"name": "RSI", "params": {"length": 14}},
                {"name": "MACD", "params": {"fast": 12, "slow": 26, "signal": 9}},
                {"name": "EMA", "params": {"length": 20}}
            ],
            "limit": 100
        }
        
        calc_result = self.test_endpoint("Calculate Indicators", "POST", "/indicators/calculate", indicator_request)
        if calc_result:
            indicators = calc_result.get('indicators', {})
            logger.info(f"Calculated indicators: {list(indicators.keys())}")
            for name, data in indicators.items():
                valid_points = sum(1 for point in data if any(v is not None for v in point['value'].values()))
                logger.info(f"  {name}: {valid_points} valid data points")
        
        # Test indicator snapshot
        if calc_result and calc_result.get('indicators'):
            # Use the first timestamp from RSI data
            rsi_data = calc_result['indicators'].get('RSI', [])
            if rsi_data:
                test_timestamp = rsi_data[10]['timestamp']  # Use 10th point
                
                snapshot_request = {
                    "symbol": TEST_SYMBOL,
                    "timeframe": TEST_TIMEFRAME,
                    "timestamp": test_timestamp,
                    "indicators": [{"name": "RSI", "params": {"length": 14}}]
                }
                
                snapshot_result = self.test_endpoint("Indicator Snapshot", "POST", "/indicators/snapshot", snapshot_request)
                if snapshot_result:
                    logger.info(f"Snapshot indicators: {list(snapshot_result.get('indicators', {}).keys())}")
    
    def test_session_management(self):
        """Test session management endpoints"""
        logger.info("\n📋 Testing Session Management")
        logger.info("-" * 40)
        
        # Create session
        session_request = {
            "symbol": TEST_SYMBOL,
            "timeframe": TEST_TIMEFRAME,
            "session_name": f"API Test Session {datetime.now().strftime('%Y%m%d_%H%M%S')}"
        }
        
        session_result = self.test_endpoint("Create Session", "POST", "/strategy/session/create", session_request)
        if session_result:
            self.session_id = session_result.get('session_id')
            logger.info(f"Created session: {self.session_id}")
        
        # List sessions
        sessions_result = self.test_endpoint("List Sessions", "GET", "/strategy/sessions")
        if sessions_result:
            logger.info(f"Total sessions: {sessions_result.get('count', 0)}")
        
        # Get session details
        if self.session_id:
            session_details = self.test_endpoint("Session Details", "GET", f"/strategy/session/{self.session_id}")
            if session_details:
                session_info = session_details.get('session', {})
                logger.info(f"Session symbol: {session_info.get('symbol')}")
                logger.info(f"Session timeframe: {session_info.get('timeframe')}")
    
    def test_trade_marking(self):
        """Test trade marking endpoints"""
        logger.info("\n📋 Testing Trade Marking")
        logger.info("-" * 40)
        
        if not self.session_id:
            logger.warning("No session available for trade marking tests")
            return
        
        # Create entry mark
        entry_request = {
            "symbol": TEST_SYMBOL,
            "timeframe": TEST_TIMEFRAME,
            "timestamp": datetime.now().isoformat(),
            "price": 45000.0,
            "entry_side": "buy",
            "session_id": self.session_id,
            "indicator_snapshot": {"RSI": {"rsi": 30.5}},
            "ohlcv_snapshot": {"open": 44900, "high": 45100, "low": 44800, "close": 45000, "volume": 1000}
        }
        
        entry_result = self.test_endpoint("Create Entry Mark", "POST", "/marks/entry", entry_request)
        if entry_result:
            self.entry_mark_id = entry_result.get('mark_id')
            logger.info(f"Created entry mark: {self.entry_mark_id}")
        
        # Create exit mark
        if self.entry_mark_id:
            exit_request = {
                "symbol": TEST_SYMBOL,
                "timeframe": TEST_TIMEFRAME,
                "timestamp": datetime.now().isoformat(),
                "price": 46000.0,
                "linked_trade_id": self.entry_mark_id,
                "session_id": self.session_id,
                "indicator_snapshot": {"RSI": {"rsi": 70.2}},
                "ohlcv_snapshot": {"open": 45900, "high": 46100, "low": 45800, "close": 46000, "volume": 1200}
            }
            
            exit_result = self.test_endpoint("Create Exit Mark", "POST", "/marks/exit", exit_request)
            if exit_result:
                trade_summary = exit_result.get('trade_summary', {})
                logger.info(f"Created exit mark, P&L: {trade_summary.get('profit_pct', 0):.2f}%")
        
        # Get session marks
        marks_result = self.test_endpoint("Session Marks", "GET", f"/marks/session/{self.session_id}")
        if marks_result:
            logger.info(f"Total marks: {marks_result.get('total_marks', 0)}")
            logger.info(f"Entry marks: {len(marks_result.get('entry_marks', []))}")
            logger.info(f"Exit marks: {len(marks_result.get('exit_marks', []))}")
        
        # Get open entries
        open_entries = self.test_endpoint("Open Entries", "GET", f"/marks/open-entries/{self.session_id}")
        if open_entries:
            logger.info(f"Open entries: {open_entries.get('count', 0)}")
    
    def test_strategy_endpoints(self):
        """Test strategy analysis endpoints"""
        logger.info("\n📋 Testing Strategy Endpoints")
        logger.info("-" * 40)
        
        if not self.session_id:
            logger.warning("No session available for strategy tests")
            return
        
        # Get session analytics
        analytics_result = self.test_endpoint("Session Analytics", "GET", f"/strategy/analytics/{self.session_id}")
        if analytics_result:
            analytics = analytics_result.get('analytics', {})
            logger.info(f"Total trades: {analytics.get('total_trades', 0)}")
            logger.info(f"Win rate: {analytics.get('win_rate', 0):.1f}%")
            logger.info(f"Total profit: {analytics.get('total_profit_pct', 0):.2f}%")
        
        # Test export endpoints (these return files, so we just check status codes)
        if analytics_result and analytics_result.get('analytics', {}).get('total_trades', 0) > 0:
            # Test JSON export
            json_export = self.test_endpoint("Export JSON", "GET", f"/strategy/export/{self.session_id}/json")
            
            # Test CSV export  
            csv_export = self.test_endpoint("Export CSV", "GET", f"/strategy/export/{self.session_id}/csv")
    
    def print_summary(self):
        """Print test summary"""
        logger.info("\n" + "=" * 50)
        logger.info("🏁 API ENDPOINT TEST SUMMARY")
        logger.info("=" * 50)
        
        logger.info("✅ All API endpoint tests completed!")
        logger.info(f"Session created: {self.session_id}")
        logger.info(f"Entry mark created: {self.entry_mark_id}")
        
        logger.info("\n🎯 Next Steps:")
        logger.info("1. Open browser: http://localhost:8000")
        logger.info("2. Test the frontend interface")
        logger.info("3. Run comprehensive tests: python tests/test_all_features.py")


def main():
    """Main test function"""
    logger.info("Strategy Builder API Endpoint Tests")
    logger.info("Make sure the server is running: python run.py")
    logger.info("")
    
    # Wait a moment for user to confirm
    input("Press Enter to start API tests (make sure server is running)...")
    
    tester = APITester()
    tester.run_all_tests()


if __name__ == "__main__":
    main()
