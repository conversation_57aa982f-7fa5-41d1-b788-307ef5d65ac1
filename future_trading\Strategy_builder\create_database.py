#!/usr/bin/env python3
"""
Simple Database Creation Script for Strategy Builder
Creates the strategy_buider database and all tables
"""
import mysql.connector
from mysql.connector import Error
import os
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Database Configuration
DB_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': '@Oppa121089',
    'database': 'strategy_buider'
}


def create_database_and_tables():
    """Create database and all tables"""
    try:
        logger.info("🚀 Creating Strategy Builder Database")
        logger.info("=" * 50)
        
        # Step 1: Connect to MySQL server (without database)
        logger.info("Step 1: Connecting to MySQL server...")
        connection = mysql.connector.connect(
            host=DB_CONFIG['host'],
            user=DB_CONFIG['user'],
            password=DB_CONFIG['password']
        )
        
        if connection.is_connected():
            cursor = connection.cursor()
            
            # Create database
            logger.info("Step 2: Creating database 'strategy_buider'...")
            cursor.execute("CREATE DATABASE IF NOT EXISTS strategy_buider")
            logger.info("✅ Database 'strategy_buider' created successfully")
            
            cursor.close()
            connection.close()
        
        # Step 3: Connect to the new database and create tables
        logger.info("Step 3: Creating tables...")
        connection = mysql.connector.connect(**DB_CONFIG)
        
        if connection.is_connected():
            cursor = connection.cursor()
            
            # Read and execute schema file
            schema_file = os.path.join(os.path.dirname(__file__), 'database', 'schema.sql')
            
            with open(schema_file, 'r') as file:
                sql_script = file.read()
            
            # Split by semicolon and execute each statement
            statements = sql_script.split(';')
            for statement in statements:
                statement = statement.strip()
                if statement and not statement.startswith('--'):
                    try:
                        cursor.execute(statement)
                        logger.debug(f"Executed: {statement[:50]}...")
                    except Error as e:
                        if "already exists" not in str(e).lower():
                            logger.warning(f"Warning executing statement: {e}")
            
            connection.commit()
            logger.info("✅ All tables created successfully")
            
            # Verify tables
            cursor.execute("SHOW TABLES")
            tables = [table[0] for table in cursor.fetchall()]
            
            expected_tables = [
                'ohlcv_data',
                'indicators_data',
                'manual_marks',
                'strategy_log',
                'strategy_sessions'
            ]
            
            logger.info("Step 4: Verifying tables...")
            for table in expected_tables:
                if table in tables:
                    logger.info(f"✅ Table '{table}' exists")
                else:
                    logger.error(f"❌ Table '{table}' missing")
            
            cursor.close()
            connection.close()
            
            logger.info("\n🎉 Database setup completed successfully!")
            logger.info("Database: strategy_buider")
            logger.info(f"Tables created: {len(tables)}")
            logger.info("Ready to run Strategy Builder!")
            
            return True
            
    except Error as e:
        logger.error(f"❌ Database creation failed: {e}")
        return False
    except FileNotFoundError:
        logger.error("❌ Schema file not found: database/schema.sql")
        return False
    except Exception as e:
        logger.error(f"❌ Unexpected error: {e}")
        return False


def test_database_connection():
    """Test database connection"""
    try:
        logger.info("Testing database connection...")
        connection = mysql.connector.connect(**DB_CONFIG)
        
        if connection.is_connected():
            cursor = connection.cursor()
            cursor.execute("SELECT VERSION()")
            version = cursor.fetchone()[0]
            
            cursor.execute("SELECT DATABASE()")
            database = cursor.fetchone()[0]
            
            logger.info(f"✅ Connected to MySQL {version}")
            logger.info(f"✅ Using database: {database}")
            
            cursor.close()
            connection.close()
            return True
            
    except Error as e:
        logger.error(f"❌ Connection test failed: {e}")
        return False


def show_table_info():
    """Show information about created tables"""
    try:
        connection = mysql.connector.connect(**DB_CONFIG)
        cursor = connection.cursor()
        
        logger.info("\n📊 Table Information:")
        logger.info("-" * 40)
        
        cursor.execute("SHOW TABLES")
        tables = [table[0] for table in cursor.fetchall()]
        
        for table in tables:
            cursor.execute(f"SELECT COUNT(*) FROM {table}")
            count = cursor.fetchone()[0]
            
            cursor.execute(f"DESCRIBE {table}")
            columns = cursor.fetchall()
            
            logger.info(f"Table: {table}")
            logger.info(f"  Records: {count}")
            logger.info(f"  Columns: {len(columns)}")
            logger.info(f"  Structure: {', '.join([col[0] for col in columns[:5]])}{'...' if len(columns) > 5 else ''}")
            logger.info("")
        
        cursor.close()
        connection.close()
        
    except Error as e:
        logger.error(f"Error showing table info: {e}")


def main():
    """Main function"""
    logger.info("Strategy Builder Database Setup")
    logger.info("Using configuration:")
    logger.info(f"  Host: {DB_CONFIG['host']}")
    logger.info(f"  User: {DB_CONFIG['user']}")
    logger.info(f"  Database: {DB_CONFIG['database']}")
    logger.info("")
    
    # Create database and tables
    if create_database_and_tables():
        # Test connection
        if test_database_connection():
            # Show table information
            show_table_info()
            
            logger.info("🎯 Next Steps:")
            logger.info("1. Run: python tests/test_all_features.py")
            logger.info("2. Start the application: python run.py")
            logger.info("3. Open browser: http://localhost:8000")
        else:
            logger.error("Database connection test failed")
    else:
        logger.error("Database setup failed")


if __name__ == "__main__":
    main()
