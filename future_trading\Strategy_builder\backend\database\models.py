"""
SQLAlchemy ORM Models for Strategy Builder
"""
from sqlalchemy import Column, Integer, String, DateTime, Decimal, Text, JSON, ForeignKey, Enum, Index
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from datetime import datetime
from typing import Optional, Dict, Any

from config.database import Base


class OHLCVData(Base):
    """OHLCV candlestick data model"""
    __tablename__ = "ohlcv_data"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    symbol = Column(String(20), nullable=False)
    timeframe = Column(String(10), nullable=False)
    timestamp = Column(DateTime, nullable=False)
    open = Column(Decimal(20, 8), nullable=False)
    high = Column(Decimal(20, 8), nullable=False)
    low = Column(Decimal(20, 8), nullable=False)
    close = Column(Decimal(20, 8), nullable=False)
    volume = Column(Decimal(20, 8), nullable=False)
    created_at = Column(DateTime, default=func.now())
    
    # Indexes
    __table_args__ = (
        Index('idx_symbol_timeframe', 'symbol', 'timeframe'),
        Index('idx_timestamp', 'timestamp'),
        Index('idx_ohlcv_lookup', 'symbol', 'timeframe', 'timestamp'),
        Index('unique_candle', 'symbol', 'timeframe', 'timestamp', unique=True)
    )
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'id': self.id,
            'symbol': self.symbol,
            'timeframe': self.timeframe,
            'timestamp': self.timestamp.isoformat(),
            'open': float(self.open),
            'high': float(self.high),
            'low': float(self.low),
            'close': float(self.close),
            'volume': float(self.volume)
        }


class IndicatorsData(Base):
    """Technical indicators data model"""
    __tablename__ = "indicators_data"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    symbol = Column(String(20), nullable=False)
    timeframe = Column(String(10), nullable=False)
    timestamp = Column(DateTime, nullable=False)
    indicator_name = Column(String(50), nullable=False)
    indicator_params = Column(JSON)
    value = Column(JSON, nullable=False)
    created_at = Column(DateTime, default=func.now())
    
    # Indexes
    __table_args__ = (
        Index('idx_symbol_timeframe_indicator', 'symbol', 'timeframe', 'indicator_name'),
        Index('idx_timestamp', 'timestamp'),
        Index('idx_indicators_lookup', 'symbol', 'timeframe', 'indicator_name', 'timestamp'),
        Index('unique_indicator', 'symbol', 'timeframe', 'timestamp', 'indicator_name', unique=True)
    )
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'id': self.id,
            'symbol': self.symbol,
            'timeframe': self.timeframe,
            'timestamp': self.timestamp.isoformat(),
            'indicator_name': self.indicator_name,
            'indicator_params': self.indicator_params,
            'value': self.value
        }


class ManualMarks(Base):
    """Manual entry/exit marks model"""
    __tablename__ = "manual_marks"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    symbol = Column(String(20), nullable=False)
    timeframe = Column(String(10), nullable=False)
    mark_type = Column(Enum('entry', 'exit', name='mark_type_enum'), nullable=False)
    entry_side = Column(Enum('buy', 'sell', name='entry_side_enum'), nullable=True)
    timestamp = Column(DateTime, nullable=False)
    price = Column(Decimal(20, 8), nullable=False)
    indicator_snapshot = Column(JSON)
    ohlcv_snapshot = Column(JSON)
    linked_trade_id = Column(Integer, nullable=True)
    session_id = Column(String(100))
    created_at = Column(DateTime, default=func.now())
    
    # Indexes
    __table_args__ = (
        Index('idx_symbol_timeframe', 'symbol', 'timeframe'),
        Index('idx_mark_type', 'mark_type'),
        Index('idx_linked_trade', 'linked_trade_id'),
        Index('idx_session', 'session_id'),
        Index('idx_marks_lookup', 'symbol', 'timeframe', 'timestamp', 'mark_type')
    )
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'id': self.id,
            'symbol': self.symbol,
            'timeframe': self.timeframe,
            'mark_type': self.mark_type,
            'entry_side': self.entry_side,
            'timestamp': self.timestamp.isoformat(),
            'price': float(self.price),
            'indicator_snapshot': self.indicator_snapshot,
            'ohlcv_snapshot': self.ohlcv_snapshot,
            'linked_trade_id': self.linked_trade_id,
            'session_id': self.session_id
        }


class StrategyLog(Base):
    """Strategy trade log model"""
    __tablename__ = "strategy_log"
    
    strategy_id = Column(Integer, primary_key=True, autoincrement=True)
    symbol = Column(String(20), nullable=False)
    timeframe = Column(String(10), nullable=False)
    entry_id = Column(Integer, ForeignKey('manual_marks.id'), nullable=False)
    exit_id = Column(Integer, ForeignKey('manual_marks.id'), nullable=False)
    entry_side = Column(Enum('buy', 'sell', name='entry_side_enum'), nullable=False)
    entry_price = Column(Decimal(20, 8), nullable=False)
    exit_price = Column(Decimal(20, 8), nullable=False)
    profit_pct = Column(Decimal(10, 4), nullable=False)
    profit_absolute = Column(Decimal(20, 8))
    entry_timestamp = Column(DateTime, nullable=False)
    exit_timestamp = Column(DateTime, nullable=False)
    trade_duration_minutes = Column(Integer)
    entry_ohlcv = Column(JSON)
    exit_ohlcv = Column(JSON)
    entry_indicator_snapshot = Column(JSON)
    exit_indicator_snapshot = Column(JSON)
    session_id = Column(String(100))
    notes = Column(Text)
    created_at = Column(DateTime, default=func.now())
    
    # Relationships
    entry_mark = relationship("ManualMarks", foreign_keys=[entry_id])
    exit_mark = relationship("ManualMarks", foreign_keys=[exit_id])
    
    # Indexes
    __table_args__ = (
        Index('idx_symbol_timeframe', 'symbol', 'timeframe'),
        Index('idx_profit', 'profit_pct'),
        Index('idx_session', 'session_id'),
        Index('idx_created_at', 'created_at'),
        Index('idx_strategy_performance', 'symbol', 'timeframe', 'profit_pct', 'created_at')
    )
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'strategy_id': self.strategy_id,
            'symbol': self.symbol,
            'timeframe': self.timeframe,
            'entry_id': self.entry_id,
            'exit_id': self.exit_id,
            'entry_side': self.entry_side,
            'entry_price': float(self.entry_price),
            'exit_price': float(self.exit_price),
            'profit_pct': float(self.profit_pct),
            'profit_absolute': float(self.profit_absolute) if self.profit_absolute else None,
            'entry_timestamp': self.entry_timestamp.isoformat(),
            'exit_timestamp': self.exit_timestamp.isoformat(),
            'trade_duration_minutes': self.trade_duration_minutes,
            'entry_ohlcv': self.entry_ohlcv,
            'exit_ohlcv': self.exit_ohlcv,
            'entry_indicator_snapshot': self.entry_indicator_snapshot,
            'exit_indicator_snapshot': self.exit_indicator_snapshot,
            'session_id': self.session_id,
            'notes': self.notes
        }


class StrategySessions(Base):
    """Strategy sessions model for grouping trades"""
    __tablename__ = "strategy_sessions"
    
    session_id = Column(String(100), primary_key=True)
    session_name = Column(String(200))
    symbol = Column(String(20), nullable=False)
    timeframe = Column(String(10), nullable=False)
    start_date = Column(DateTime)
    end_date = Column(DateTime)
    total_trades = Column(Integer, default=0)
    winning_trades = Column(Integer, default=0)
    losing_trades = Column(Integer, default=0)
    total_profit_pct = Column(Decimal(10, 4), default=0)
    win_rate = Column(Decimal(5, 2), default=0)
    avg_profit_per_trade = Column(Decimal(10, 4), default=0)
    max_drawdown = Column(Decimal(10, 4), default=0)
    indicators_used = Column(JSON)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # Indexes
    __table_args__ = (
        Index('idx_symbol_timeframe', 'symbol', 'timeframe'),
        Index('idx_created_at', 'created_at')
    )
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'session_id': self.session_id,
            'session_name': self.session_name,
            'symbol': self.symbol,
            'timeframe': self.timeframe,
            'start_date': self.start_date.isoformat() if self.start_date else None,
            'end_date': self.end_date.isoformat() if self.end_date else None,
            'total_trades': self.total_trades,
            'winning_trades': self.winning_trades,
            'losing_trades': self.losing_trades,
            'total_profit_pct': float(self.total_profit_pct),
            'win_rate': float(self.win_rate),
            'avg_profit_per_trade': float(self.avg_profit_per_trade),
            'max_drawdown': float(self.max_drawdown),
            'indicators_used': self.indicators_used
        }
