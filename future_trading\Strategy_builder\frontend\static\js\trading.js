/**
 * Trading Management for Strategy Builder
 */

class TradingManager {
    constructor() {
        this.currentSession = null;
        this.openTrades = new Map();
        this.tradeHistory = [];
        this.setupEventListeners();
        this.loadCurrentSession();
    }

    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Entry marking buttons
        document.getElementById('mark-entry-buy-btn')?.addEventListener('click', () => {
            this.markEntry('buy');
        });

        document.getElementById('mark-entry-sell-btn')?.addEventListener('click', () => {
            this.markEntry('sell');
        });

        // Exit marking button
        document.getElementById('mark-exit-btn')?.addEventListener('click', () => {
            this.markExit();
        });

        // Session management
        document.getElementById('new-session-btn')?.addEventListener('click', () => {
            this.showNewSessionModal();
        });

        document.getElementById('create-session-btn')?.addEventListener('click', () => {
            this.createNewSession();
        });

        // Export buttons
        document.getElementById('export-json-btn')?.addEventListener('click', () => {
            this.exportData('json');
        });

        document.getElementById('export-csv-btn')?.addEventListener('click', () => {
            this.exportData('csv');
        });

        // Modal close handlers
        document.querySelectorAll('.modal .close, .modal-close').forEach(element => {
            element.addEventListener('click', (e) => {
                const modal = e.target.closest('.modal');
                if (modal) {
                    modal.style.display = 'none';
                }
            });
        });

        // Click outside modal to close
        window.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal')) {
                e.target.style.display = 'none';
            }
        });
    }

    /**
     * Load current session from storage
     */
    loadCurrentSession() {
        const sessionData = UTILS.getFromStorage(CONFIG.STORAGE_KEYS.CURRENT_SESSION);
        if (sessionData) {
            this.currentSession = sessionData;
            this.updateSessionDisplay();
            this.loadSessionData();
        }
    }

    /**
     * Show new session modal
     */
    showNewSessionModal() {
        const modal = document.getElementById('new-session-modal');
        if (modal) {
            // Pre-fill with current chart data
            const symbolInput = document.getElementById('session-symbol-input');
            const timeframeSelect = document.getElementById('session-timeframe-select');

            // Check if chartManager is available
            if (window.chartManager) {
                if (symbolInput) symbolInput.value = window.chartManager.currentSymbol || 'BTCUSDT';
                if (timeframeSelect) timeframeSelect.value = window.chartManager.currentTimeframe || '1h';
            } else {
                // Fallback values if chartManager is not available
                if (symbolInput) symbolInput.value = 'BTCUSDT';
                if (timeframeSelect) timeframeSelect.value = '1h';
            }

            modal.style.display = 'block';
        }
    }

    /**
     * Create new session
     */
    async createNewSession() {
        try {
            const sessionName = document.getElementById('session-name-input')?.value;
            const symbol = document.getElementById('session-symbol-input')?.value;
            const timeframe = document.getElementById('session-timeframe-select')?.value;

            if (!symbol || !timeframe) {
                UTILS.showNotification('Please fill in all required fields', 'error');
                return;
            }

            UTILS.showLoading('Creating session...');

            const response = await api.createSession(symbol, timeframe, sessionName);
            
            this.currentSession = {
                session_id: response.session_id,
                session_name: response.session_name,
                symbol: response.symbol,
                timeframe: response.timeframe,
                created_at: response.created_at
            };

            // Save to storage
            UTILS.saveToStorage(CONFIG.STORAGE_KEYS.CURRENT_SESSION, this.currentSession);

            // Update UI
            this.updateSessionDisplay();
            this.clearTradeData();

            // Close modal
            document.getElementById('new-session-modal').style.display = 'none';

            UTILS.hideLoading();
            UTILS.showNotification(CONFIG.SUCCESS_MESSAGES.SESSION_CREATED, 'success');

        } catch (error) {
            UTILS.hideLoading();
            UTILS.showNotification(`Error creating session: ${error.message}`, 'error');
        }
    }

    /**
     * Update session display
     */
    updateSessionDisplay() {
        const sessionNameElement = document.getElementById('session-name');
        if (sessionNameElement && this.currentSession) {
            sessionNameElement.textContent = this.currentSession.session_name || 
                `${this.currentSession.symbol}_${this.currentSession.timeframe}`;
        }
    }

    /**
     * Mark entry point
     */
    async markEntry(side) {
        try {
            if (!this.currentSession) {
                UTILS.showNotification('Please create a session first', 'error');
                this.showNewSessionModal();
                return;
            }

            const clickData = chartManager.getLastClickData();
            if (!clickData) {
                UTILS.showNotification('Please click on the chart first', 'warning');
                return;
            }

            UTILS.showLoading('Marking entry...');

            // Get indicator snapshot
            const indicatorSnapshot = await indicatorManager.getIndicatorSnapshot(clickData.timestamp);

            // Get OHLCV snapshot
            const ohlcvSnapshot = this.getOHLCVSnapshot(clickData.timestamp);

            const response = await api.createEntryMark(
                this.currentSession.symbol,
                this.currentSession.timeframe,
                clickData.timestamp,
                clickData.price,
                side,
                this.currentSession.session_id,
                {
                    indicators: indicatorSnapshot,
                    ohlcv: ohlcvSnapshot
                }
            );

            // Add to open trades
            this.openTrades.set(response.mark_id, {
                id: response.mark_id,
                side: side,
                price: clickData.price,
                timestamp: clickData.timestamp,
                symbol: this.currentSession.symbol,
                timeframe: this.currentSession.timeframe
            });

            // Add marker to chart
            chartManager.addTradeMarker(clickData.timestamp, clickData.price, 'entry', side);

            // Update UI
            this.updateOpenTradesDisplay();
            this.enableExitButton();

            UTILS.hideLoading();
            UTILS.showNotification(CONFIG.SUCCESS_MESSAGES.ENTRY_MARKED, 'success');

        } catch (error) {
            UTILS.hideLoading();
            UTILS.showNotification(`Error marking entry: ${error.message}`, 'error');
        }
    }

    /**
     * Mark exit point
     */
    async markExit() {
        try {
            if (this.openTrades.size === 0) {
                UTILS.showNotification('No open trades to exit', 'warning');
                return;
            }

            const clickData = chartManager.getLastClickData();
            if (!clickData) {
                UTILS.showNotification('Please click on the chart first', 'warning');
                return;
            }

            // For simplicity, exit the first open trade
            // In a full implementation, you'd show a selection dialog
            const [tradeId, tradeData] = this.openTrades.entries().next().value;

            UTILS.showLoading('Marking exit...');

            // Get indicator snapshot
            const indicatorSnapshot = await indicatorManager.getIndicatorSnapshot(clickData.timestamp);

            // Get OHLCV snapshot
            const ohlcvSnapshot = this.getOHLCVSnapshot(clickData.timestamp);

            const response = await api.createExitMark(
                this.currentSession.symbol,
                this.currentSession.timeframe,
                clickData.timestamp,
                clickData.price,
                tradeId,
                this.currentSession.session_id,
                {
                    indicators: indicatorSnapshot,
                    ohlcv: ohlcvSnapshot
                }
            );

            // Remove from open trades
            this.openTrades.delete(tradeId);

            // Add to trade history
            this.tradeHistory.push({
                ...tradeData,
                exit_price: clickData.price,
                exit_timestamp: clickData.timestamp,
                profit_pct: response.trade_summary.profit_pct,
                profit_absolute: response.trade_summary.profit_absolute
            });

            // Add marker to chart
            chartManager.addTradeMarker(clickData.timestamp, clickData.price, 'exit');

            // Update UI
            this.updateOpenTradesDisplay();
            this.updateTradeHistoryDisplay();
            this.updateTradeStats();

            if (this.openTrades.size === 0) {
                this.disableExitButton();
            }

            UTILS.hideLoading();
            UTILS.showNotification(CONFIG.SUCCESS_MESSAGES.EXIT_MARKED, 'success');

        } catch (error) {
            UTILS.hideLoading();
            UTILS.showNotification(`Error marking exit: ${error.message}`, 'error');
        }
    }

    /**
     * Get OHLCV snapshot for timestamp
     */
    getOHLCVSnapshot(timestamp) {
        const currentData = chartManager.getCurrentData();
        if (!currentData) return {};

        // Find the candle for the given timestamp
        const targetTime = new Date(timestamp).getTime();
        const candle = currentData.find(c => {
            const candleTime = new Date(c.timestamp).getTime();
            return Math.abs(candleTime - targetTime) < 60000; // Within 1 minute
        });

        return candle || {};
    }

    /**
     * Update open trades display
     */
    updateOpenTradesDisplay() {
        const container = document.getElementById('open-trades-list');
        if (!container) return;

        container.innerHTML = '';

        this.openTrades.forEach((trade, id) => {
            const tradeElement = document.createElement('div');
            tradeElement.className = 'open-trade-item';
            tradeElement.innerHTML = `
                <div>${trade.side.toUpperCase()} @ ${UTILS.formatNumber(trade.price, 4)}</div>
                <div>${UTILS.formatTimestamp(trade.timestamp)}</div>
                <button onclick="tradingManager.selectTradeForExit(${id})" class="btn btn-warning btn-sm">Exit</button>
            `;
            container.appendChild(tradeElement);
        });
    }

    /**
     * Update trade history display
     */
    updateTradeHistoryDisplay() {
        const container = document.getElementById('trade-list');
        if (!container) return;

        container.innerHTML = '';

        this.tradeHistory.slice(-10).reverse().forEach(trade => {
            const tradeElement = document.createElement('div');
            tradeElement.className = `trade-item ${trade.profit_pct > 0 ? 'profit' : 'loss'}`;
            tradeElement.innerHTML = `
                <div class="trade-header">
                    <span>${trade.side.toUpperCase()}</span>
                    <span class="${trade.profit_pct > 0 ? 'text-success' : 'text-danger'}">
                        ${UTILS.formatPercent(trade.profit_pct)}
                    </span>
                </div>
                <div class="trade-details">
                    <div>Entry: ${UTILS.formatNumber(trade.price, 4)}</div>
                    <div>Exit: ${UTILS.formatNumber(trade.exit_price, 4)}</div>
                </div>
            `;
            container.appendChild(tradeElement);
        });
    }

    /**
     * Update trade statistics
     */
    updateTradeStats() {
        const totalTrades = this.tradeHistory.length;
        const winningTrades = this.tradeHistory.filter(t => t.profit_pct > 0).length;
        const winRate = totalTrades > 0 ? (winningTrades / totalTrades) * 100 : 0;
        const totalPnL = this.tradeHistory.reduce((sum, t) => sum + t.profit_pct, 0);

        // Update UI elements
        const totalTradesElement = document.getElementById('total-trades');
        const winRateElement = document.getElementById('win-rate');
        const totalPnLElement = document.getElementById('total-pnl');

        if (totalTradesElement) totalTradesElement.textContent = totalTrades;
        if (winRateElement) winRateElement.textContent = UTILS.formatPercent(winRate);
        if (totalPnLElement) {
            totalPnLElement.textContent = UTILS.formatPercent(totalPnL);
            totalPnLElement.className = totalPnL >= 0 ? 'text-success' : 'text-danger';
        }
    }

    /**
     * Enable/disable exit button
     */
    enableExitButton() {
        const exitBtn = document.getElementById('mark-exit-btn');
        if (exitBtn) exitBtn.disabled = false;
    }

    disableExitButton() {
        const exitBtn = document.getElementById('mark-exit-btn');
        if (exitBtn) exitBtn.disabled = true;
    }

    /**
     * Select trade for exit (for multiple open trades)
     */
    selectTradeForExit(tradeId) {
        // This would implement trade selection logic
        console.log('Selected trade for exit:', tradeId);
    }

    /**
     * Load session data
     */
    async loadSessionData() {
        if (!this.currentSession) return;

        try {
            // Load session marks and trades
            const sessionData = await api.getSession(this.currentSession.session_id);
            
            // Process marks and update displays
            if (sessionData.marks) {
                this.processSessionMarks(sessionData.marks);
            }

            if (sessionData.strategy_logs) {
                this.tradeHistory = sessionData.strategy_logs;
                this.updateTradeHistoryDisplay();
                this.updateTradeStats();
            }

        } catch (error) {
            console.error('Error loading session data:', error);
        }
    }

    /**
     * Process session marks
     */
    processSessionMarks(marks) {
        // Clear existing data
        this.openTrades.clear();
        chartManager.clearMarkers();

        // Process marks
        marks.forEach(mark => {
            if (mark.mark_type === 'entry') {
                // Check if this entry has a corresponding exit
                const hasExit = marks.some(m => 
                    m.mark_type === 'exit' && m.linked_trade_id === mark.id
                );

                if (!hasExit) {
                    // Add to open trades
                    this.openTrades.set(mark.id, {
                        id: mark.id,
                        side: mark.entry_side,
                        price: mark.price,
                        timestamp: mark.timestamp,
                        symbol: mark.symbol,
                        timeframe: mark.timeframe
                    });
                }

                // Add marker to chart
                chartManager.addTradeMarker(mark.timestamp, mark.price, 'entry', mark.entry_side);
            } else if (mark.mark_type === 'exit') {
                // Add exit marker
                chartManager.addTradeMarker(mark.timestamp, mark.price, 'exit');
            }
        });

        // Update displays
        this.updateOpenTradesDisplay();
        if (this.openTrades.size > 0) {
            this.enableExitButton();
        }
    }

    /**
     * Export data
     */
    async exportData(format) {
        if (!this.currentSession) {
            UTILS.showNotification('No session to export', 'warning');
            return;
        }

        try {
            UTILS.showLoading(`Exporting ${format.toUpperCase()}...`);

            if (format === 'json') {
                await api.exportSessionJSON(this.currentSession.session_id);
            } else if (format === 'csv') {
                await api.exportSessionCSV(this.currentSession.session_id);
            }

            UTILS.hideLoading();
            UTILS.showNotification(CONFIG.SUCCESS_MESSAGES.DATA_EXPORTED, 'success');

        } catch (error) {
            UTILS.hideLoading();
            UTILS.showNotification(`Error exporting data: ${error.message}`, 'error');
        }
    }

    /**
     * Clear trade data
     */
    clearTradeData() {
        this.openTrades.clear();
        this.tradeHistory = [];
        chartManager.clearMarkers();
        this.updateOpenTradesDisplay();
        this.updateTradeHistoryDisplay();
        this.updateTradeStats();
        this.disableExitButton();
    }
}

// Create global trading manager instance
const tradingManager = new TradingManager();

// Export for use in other modules
window.tradingManager = tradingManager;
