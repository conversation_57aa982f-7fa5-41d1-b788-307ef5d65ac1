"""
Strategy Builder Configuration Settings
"""
import os
from typing import List, Optional
from pydantic_settings import BaseSettings
from pydantic import Field


class DatabaseSettings(BaseSettings):
    """Database configuration"""
    model_config = {"extra": "ignore", "env_file": ".env"}

    host: str = Field(default="localhost", alias="DB_HOST")
    port: int = Field(default=3306, alias="DB_PORT")
    username: str = Field(default="root", alias="DB_USERNAME")
    password: str = Field(default="@Oppa121089", alias="DB_PASSWORD")
    database: str = Field(default="strategy_buider", alias="DB_DATABASE")
    
    @property
    def url(self) -> str:
        from urllib.parse import quote_plus
        # URL encode the password to handle special characters like @
        encoded_password = quote_plus(self.password)
        return f"mysql+pymysql://{self.username}:{encoded_password}@{self.host}:{self.port}/{self.database}"


class BinanceSettings(BaseSettings):
    """Binance API configuration"""
    model_config = {"extra": "ignore"}

    api_key: str = Field(default="", env="BINANCE_API_KEY")
    api_secret: str = Field(default="", env="BINANCE_API_SECRET")
    testnet: bool = Field(default=True, env="BINANCE_TESTNET")
    base_url: str = Field(default="https://testnet.binancefuture.com", env="BINANCE_BASE_URL")


class MexcSettings(BaseSettings):
    """MEXC API configuration"""
    model_config = {"extra": "ignore"}

    api_key: str = Field(default="", env="MEXC_API_KEY")
    api_secret: str = Field(default="", env="MEXC_API_SECRET")
    base_url: str = Field(default="https://api.mexc.com", env="MEXC_BASE_URL")


class AppSettings(BaseSettings):
    """Application configuration"""
    model_config = {"extra": "ignore"}

    app_name: str = "Strategy Builder"
    version: str = "1.0.0"
    debug: bool = Field(default=True, env="DEBUG")
    host: str = Field(default="127.0.0.1", env="HOST")
    port: int = Field(default=8000, env="PORT")
    
    # CORS settings
    cors_origins: List[str] = ["http://localhost:3000", "http://127.0.0.1:3000", "*"]
    
    # Chart settings
    default_symbol: str = "BTCUSDT"
    default_timeframe: str = "1h"
    default_candles: int = 500
    max_candles: int = 5000
    
    # Supported timeframes
    supported_timeframes: List[str] = ["1m", "3m", "5m", "15m", "30m", "1h", "2h", "4h", "1d", "1w"]
    
    # Supported indicators
    supported_indicators: List[str] = ["RSI", "MACD", "EMA", "SMA", "BB", "STOCH"]
    
    # Default indicator parameters
    default_indicator_params: dict = {
        "RSI": {"length": 14},
        "MACD": {"fast": 12, "slow": 26, "signal": 9},
        "EMA": {"length": 20},
        "SMA": {"length": 20},
        "BB": {"length": 20, "std": 2},
        "STOCH": {"k": 14, "d": 3, "smooth_k": 3}
    }
    
    model_config = {"extra": "ignore", "env_file": ".env", "case_sensitive": False}


class Settings:
    """Main settings class"""
    def __init__(self):
        self.app = AppSettings()
        self.database = DatabaseSettings()
        self.binance = BinanceSettings()
        self.mexc = MexcSettings()


# Global settings instance
settings = Settings()


# Environment-specific configurations
def get_settings() -> Settings:
    """Get application settings"""
    return settings


def get_database_url() -> str:
    """Get database connection URL"""
    return settings.database.url


def get_cors_origins() -> List[str]:
    """Get CORS origins"""
    return settings.app.cors_origins


def is_debug_mode() -> bool:
    """Check if debug mode is enabled"""
    return settings.app.debug


# Validation functions
def validate_symbol(symbol: str) -> bool:
    """Validate trading symbol format"""
    return len(symbol) >= 6 and symbol.isupper()


def validate_timeframe(timeframe: str) -> bool:
    """Validate timeframe"""
    return timeframe in settings.app.supported_timeframes


def validate_indicator(indicator: str) -> bool:
    """Validate indicator name"""
    return indicator.upper() in settings.app.supported_indicators
