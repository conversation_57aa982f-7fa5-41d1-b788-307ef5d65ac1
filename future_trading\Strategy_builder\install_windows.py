#!/usr/bin/env python3
"""
Windows Installation Script for Strategy Builder
Handles pip upgrade and package installation issues on Windows
"""
import sys
import subprocess
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def run_command(command, description):
    """Run a command and return success status"""
    try:
        logger.info(f"Running: {description}")
        logger.info(f"Command: {command}")
        
        result = subprocess.run(
            command,
            shell=True,
            capture_output=True,
            text=True,
            timeout=300
        )
        
        if result.returncode == 0:
            logger.info(f"SUCCESS: {description}")
            if result.stdout:
                logger.info(f"Output: {result.stdout[:200]}...")
            return True
        else:
            logger.error(f"FAILED: {description}")
            if result.stderr:
                logger.error(f"Error: {result.stderr[:200]}...")
            return False
            
    except subprocess.TimeoutExpired:
        logger.error(f"TIMEOUT: {description}")
        return False
    except Exception as e:
        logger.error(f"ERROR: {description} - {e}")
        return False


def upgrade_pip():
    """Upgrade pip to latest version"""
    logger.info("Step 1: Upgrading pip")
    logger.info("-" * 30)
    
    return run_command(
        f"{sys.executable} -m pip install --upgrade pip",
        "Upgrade pip"
    )


def install_build_tools():
    """Install build tools first"""
    logger.info("\nStep 2: Installing build tools")
    logger.info("-" * 30)
    
    tools = ["setuptools", "wheel"]
    
    for tool in tools:
        if not run_command(
            f"{sys.executable} -m pip install --upgrade {tool}",
            f"Install {tool}"
        ):
            return False
    
    return True


def install_core_packages():
    """Install core packages one by one"""
    logger.info("\nStep 3: Installing core packages")
    logger.info("-" * 30)
    
    # Core packages in order of dependency
    packages = [
        "pydantic",
        "fastapi",
        "uvicorn[standard]",
        "python-multipart",
        "requests",
        "python-dotenv",
        "python-dateutil",
        "SQLAlchemy",
        "mysql-connector-python"
    ]
    
    for package in packages:
        if not run_command(
            f"{sys.executable} -m pip install {package}",
            f"Install {package}"
        ):
            logger.warning(f"Failed to install {package}, continuing...")
    
    return True


def install_data_packages():
    """Install data processing packages"""
    logger.info("\nStep 4: Installing data processing packages")
    logger.info("-" * 30)
    
    # Try to install numpy first (most problematic)
    if not run_command(
        f"{sys.executable} -m pip install numpy",
        "Install numpy"
    ):
        logger.warning("Failed to install numpy, trying with --only-binary=all")
        if not run_command(
            f"{sys.executable} -m pip install --only-binary=all numpy",
            "Install numpy (binary only)"
        ):
            logger.error("Failed to install numpy")
            return False
    
    # Install pandas
    if not run_command(
        f"{sys.executable} -m pip install pandas",
        "Install pandas"
    ):
        logger.warning("Failed to install pandas")
        return False
    
    # Install pandas-ta
    if not run_command(
        f"{sys.executable} -m pip install pandas-ta",
        "Install pandas-ta"
    ):
        logger.warning("Failed to install pandas-ta, trying alternative")
        # Try installing from git if PyPI version fails
        run_command(
            f"{sys.executable} -m pip install git+https://github.com/twopirllc/pandas-ta.git",
            "Install pandas-ta from git"
        )
    
    return True


def verify_installation():
    """Verify that all packages are installed correctly"""
    logger.info("\nStep 5: Verifying installation")
    logger.info("-" * 30)
    
    packages_to_test = [
        ("fastapi", "fastapi"),
        ("uvicorn", "uvicorn"),
        ("pydantic", "pydantic"),
        ("mysql.connector", "mysql-connector-python"),
        ("pandas", "pandas"),
        ("numpy", "numpy"),
        ("requests", "requests"),
        ("sqlalchemy", "SQLAlchemy")
    ]
    
    failed_packages = []
    
    for import_name, package_name in packages_to_test:
        try:
            __import__(import_name)
            logger.info(f"OK: {package_name}")
        except ImportError:
            logger.error(f"FAILED: {package_name}")
            failed_packages.append(package_name)
    
    # Test pandas-ta separately (it's optional)
    try:
        import pandas_ta
        logger.info("OK: pandas-ta")
    except ImportError:
        logger.warning("WARNING: pandas-ta not available (optional)")
    
    if failed_packages:
        logger.error(f"Failed to install: {', '.join(failed_packages)}")
        return False
    else:
        logger.info("All core packages installed successfully!")
        return True


def main():
    """Main installation function"""
    logger.info("Strategy Builder - Windows Installation")
    logger.info("=" * 50)
    logger.info("This script will install all required packages for Windows")
    logger.info("=" * 50)
    
    steps = [
        ("Upgrade pip", upgrade_pip),
        ("Install build tools", install_build_tools),
        ("Install core packages", install_core_packages),
        ("Install data packages", install_data_packages),
        ("Verify installation", verify_installation)
    ]
    
    for step_name, step_func in steps:
        try:
            if not step_func():
                logger.error(f"Step failed: {step_name}")
                logger.info("You can try running the individual commands manually")
                return False
        except Exception as e:
            logger.error(f"Step error: {step_name} - {e}")
            return False
    
    logger.info("\n" + "=" * 50)
    logger.info("INSTALLATION COMPLETE!")
    logger.info("=" * 50)
    logger.info("Next steps:")
    logger.info("1. python check_requirements.py")
    logger.info("2. python create_database.py")
    logger.info("3. python test_windows.py")
    logger.info("4. python run.py")
    
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
