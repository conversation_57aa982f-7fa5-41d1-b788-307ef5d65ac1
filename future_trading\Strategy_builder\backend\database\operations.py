"""
Database operations for Strategy Builder
"""
from sqlalchemy.orm import Session
from sqlalchemy import and_, desc, asc
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
import uuid
import logging

from .models import OHLCVData, IndicatorsData, ManualMarks, StrategyLog, StrategySessions
from config.database import get_db_session

logger = logging.getLogger(__name__)


class OHLCVOperations:
    """OHLCV data operations"""
    
    @staticmethod
    def insert_ohlcv_data(db: Session, symbol: str, timeframe: str, data: List[Dict]) -> int:
        """Insert OHLCV data into database"""
        try:
            inserted_count = 0
            for candle in data:
                existing = db.query(OHLCVData).filter(
                    and_(
                        OHLCVData.symbol == symbol,
                        OHLCVData.timeframe == timeframe,
                        OHLCVData.timestamp == candle['timestamp']
                    )
                ).first()
                
                if not existing:
                    ohlcv = OHLCVData(
                        symbol=symbol,
                        timeframe=timeframe,
                        timestamp=candle['timestamp'],
                        open=candle['open'],
                        high=candle['high'],
                        low=candle['low'],
                        close=candle['close'],
                        volume=candle['volume']
                    )
                    db.add(ohlcv)
                    inserted_count += 1
            
            db.commit()
            logger.info(f"Inserted {inserted_count} OHLCV records for {symbol} {timeframe}")
            return inserted_count
        except Exception as e:
            db.rollback()
            logger.error(f"Error inserting OHLCV data: {e}")
            raise
    
    @staticmethod
    def get_ohlcv_data(db: Session, symbol: str, timeframe: str, 
                       start_time: Optional[datetime] = None, 
                       end_time: Optional[datetime] = None,
                       limit: int = 1000) -> List[Dict]:
        """Get OHLCV data from database"""
        try:
            query = db.query(OHLCVData).filter(
                and_(
                    OHLCVData.symbol == symbol,
                    OHLCVData.timeframe == timeframe
                )
            )
            
            if start_time:
                query = query.filter(OHLCVData.timestamp >= start_time)
            if end_time:
                query = query.filter(OHLCVData.timestamp <= end_time)
            
            query = query.order_by(asc(OHLCVData.timestamp)).limit(limit)
            
            results = query.all()
            return [result.to_dict() for result in results]
        except Exception as e:
            logger.error(f"Error getting OHLCV data: {e}")
            raise


class IndicatorOperations:
    """Technical indicator operations"""
    
    @staticmethod
    def insert_indicator_data(db: Session, symbol: str, timeframe: str, 
                            indicator_name: str, indicator_params: Dict,
                            data: List[Dict]) -> int:
        """Insert indicator data into database"""
        try:
            inserted_count = 0
            for item in data:
                existing = db.query(IndicatorsData).filter(
                    and_(
                        IndicatorsData.symbol == symbol,
                        IndicatorsData.timeframe == timeframe,
                        IndicatorsData.timestamp == item['timestamp'],
                        IndicatorsData.indicator_name == indicator_name
                    )
                ).first()
                
                if not existing:
                    indicator = IndicatorsData(
                        symbol=symbol,
                        timeframe=timeframe,
                        timestamp=item['timestamp'],
                        indicator_name=indicator_name,
                        indicator_params=indicator_params,
                        value=item['value']
                    )
                    db.add(indicator)
                    inserted_count += 1
            
            db.commit()
            logger.info(f"Inserted {inserted_count} {indicator_name} records for {symbol} {timeframe}")
            return inserted_count
        except Exception as e:
            db.rollback()
            logger.error(f"Error inserting indicator data: {e}")
            raise
    
    @staticmethod
    def get_indicator_data(db: Session, symbol: str, timeframe: str, 
                          indicator_name: str, timestamp: datetime) -> Optional[Dict]:
        """Get indicator data for specific timestamp"""
        try:
            result = db.query(IndicatorsData).filter(
                and_(
                    IndicatorsData.symbol == symbol,
                    IndicatorsData.timeframe == timeframe,
                    IndicatorsData.indicator_name == indicator_name,
                    IndicatorsData.timestamp == timestamp
                )
            ).first()
            
            return result.to_dict() if result else None
        except Exception as e:
            logger.error(f"Error getting indicator data: {e}")
            raise


class MarkOperations:
    """Manual mark operations"""
    
    @staticmethod
    def create_entry_mark(db: Session, symbol: str, timeframe: str, 
                         timestamp: datetime, price: float, entry_side: str,
                         indicator_snapshot: Dict, ohlcv_snapshot: Dict,
                         session_id: str) -> int:
        """Create entry mark"""
        try:
            mark = ManualMarks(
                symbol=symbol,
                timeframe=timeframe,
                mark_type='entry',
                entry_side=entry_side,
                timestamp=timestamp,
                price=price,
                indicator_snapshot=indicator_snapshot,
                ohlcv_snapshot=ohlcv_snapshot,
                session_id=session_id
            )
            db.add(mark)
            db.commit()
            db.refresh(mark)
            
            logger.info(f"Created entry mark {mark.id} for {symbol} at {timestamp}")
            return mark.id
        except Exception as e:
            db.rollback()
            logger.error(f"Error creating entry mark: {e}")
            raise
    
    @staticmethod
    def create_exit_mark(db: Session, symbol: str, timeframe: str,
                        timestamp: datetime, price: float,
                        indicator_snapshot: Dict, ohlcv_snapshot: Dict,
                        linked_trade_id: int, session_id: str) -> int:
        """Create exit mark"""
        try:
            mark = ManualMarks(
                symbol=symbol,
                timeframe=timeframe,
                mark_type='exit',
                timestamp=timestamp,
                price=price,
                indicator_snapshot=indicator_snapshot,
                ohlcv_snapshot=ohlcv_snapshot,
                linked_trade_id=linked_trade_id,
                session_id=session_id
            )
            db.add(mark)
            db.commit()
            db.refresh(mark)
            
            logger.info(f"Created exit mark {mark.id} for {symbol} at {timestamp}")
            return mark.id
        except Exception as e:
            db.rollback()
            logger.error(f"Error creating exit mark: {e}")
            raise
    
    @staticmethod
    def get_marks_by_session(db: Session, session_id: str) -> List[Dict]:
        """Get all marks for a session"""
        try:
            results = db.query(ManualMarks).filter(
                ManualMarks.session_id == session_id
            ).order_by(asc(ManualMarks.timestamp)).all()
            
            return [result.to_dict() for result in results]
        except Exception as e:
            logger.error(f"Error getting marks by session: {e}")
            raise


class StrategyOperations:
    """Strategy log operations"""
    
    @staticmethod
    def create_strategy_log(db: Session, entry_mark: ManualMarks, exit_mark: ManualMarks,
                          profit_pct: float, profit_absolute: float = None) -> int:
        """Create strategy log entry"""
        try:
            # Calculate trade duration
            duration = (exit_mark.timestamp - entry_mark.timestamp).total_seconds() / 60
            
            strategy_log = StrategyLog(
                symbol=entry_mark.symbol,
                timeframe=entry_mark.timeframe,
                entry_id=entry_mark.id,
                exit_id=exit_mark.id,
                entry_side=entry_mark.entry_side,
                entry_price=entry_mark.price,
                exit_price=exit_mark.price,
                profit_pct=profit_pct,
                profit_absolute=profit_absolute,
                entry_timestamp=entry_mark.timestamp,
                exit_timestamp=exit_mark.timestamp,
                trade_duration_minutes=int(duration),
                entry_ohlcv=entry_mark.ohlcv_snapshot,
                exit_ohlcv=exit_mark.ohlcv_snapshot,
                entry_indicator_snapshot=entry_mark.indicator_snapshot,
                exit_indicator_snapshot=exit_mark.indicator_snapshot,
                session_id=entry_mark.session_id
            )
            
            db.add(strategy_log)
            db.commit()
            db.refresh(strategy_log)
            
            logger.info(f"Created strategy log {strategy_log.strategy_id}")
            return strategy_log.strategy_id
        except Exception as e:
            db.rollback()
            logger.error(f"Error creating strategy log: {e}")
            raise
    
    @staticmethod
    def get_strategy_logs_by_session(db: Session, session_id: str) -> List[Dict]:
        """Get strategy logs for a session"""
        try:
            results = db.query(StrategyLog).filter(
                StrategyLog.session_id == session_id
            ).order_by(desc(StrategyLog.created_at)).all()
            
            return [result.to_dict() for result in results]
        except Exception as e:
            logger.error(f"Error getting strategy logs: {e}")
            raise


class SessionOperations:
    """Strategy session operations"""
    
    @staticmethod
    def create_session(db: Session, symbol: str, timeframe: str, 
                      session_name: str = None) -> str:
        """Create new strategy session"""
        try:
            session_id = str(uuid.uuid4())
            session = StrategySessions(
                session_id=session_id,
                session_name=session_name or f"{symbol}_{timeframe}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                symbol=symbol,
                timeframe=timeframe,
                start_date=datetime.now()
            )
            
            db.add(session)
            db.commit()
            
            logger.info(f"Created session {session_id}")
            return session_id
        except Exception as e:
            db.rollback()
            logger.error(f"Error creating session: {e}")
            raise
    
    @staticmethod
    def update_session_stats(db: Session, session_id: str):
        """Update session statistics"""
        try:
            # Get all strategy logs for this session
            logs = db.query(StrategyLog).filter(StrategyLog.session_id == session_id).all()
            
            if not logs:
                return
            
            total_trades = len(logs)
            winning_trades = len([log for log in logs if log.profit_pct > 0])
            losing_trades = total_trades - winning_trades
            total_profit = sum([log.profit_pct for log in logs])
            win_rate = (winning_trades / total_trades) * 100 if total_trades > 0 else 0
            avg_profit = total_profit / total_trades if total_trades > 0 else 0
            
            # Update session
            session = db.query(StrategySessions).filter(
                StrategySessions.session_id == session_id
            ).first()
            
            if session:
                session.total_trades = total_trades
                session.winning_trades = winning_trades
                session.losing_trades = losing_trades
                session.total_profit_pct = total_profit
                session.win_rate = win_rate
                session.avg_profit_per_trade = avg_profit
                session.updated_at = datetime.now()
                
                db.commit()
                logger.info(f"Updated session {session_id} stats")
        except Exception as e:
            db.rollback()
            logger.error(f"Error updating session stats: {e}")
            raise
