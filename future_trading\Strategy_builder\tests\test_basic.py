#!/usr/bin/env python3
"""
Basic Test Script for Strategy Builder
Tests essential functionality without complex database operations
"""
import sys
import os
import asyncio
import logging
from datetime import datetime

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.settings import settings
from config.database import test_connection, health_check
from backend.services.binance_service import binance_service
from backend.services.mexc_service import mexc_service, data_service

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class BasicStrategyBuilderTest:
    """Basic test suite for Strategy Builder"""
    
    def __init__(self):
        self.test_symbol = "BTCUSDT"
        self.test_timeframe = "1h"
        self.test_exchange = "binance"
        self.results = {}
    
    async def run_all_tests(self):
        """Run all basic tests"""
        logger.info("🧪 Starting Basic Strategy Builder Tests")
        logger.info("=" * 50)
        
        tests = [
            ("Database Connection", self.test_database_connection),
            ("Database Health", self.test_database_health),
            ("Database Schema", self.test_database_schema),
            ("Exchange APIs", self.test_exchange_apis),
            ("OHLCV Data Fetch", self.test_ohlcv_fetch),
            ("Settings Configuration", self.test_settings),
        ]
        
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            try:
                logger.info(f"\n📋 Testing {test_name}")
                logger.info("-" * 30)
                
                result = await test_func()
                
                if result:
                    logger.info(f"✅ {test_name}: PASSED")
                    passed += 1
                else:
                    logger.error(f"❌ {test_name}: FAILED")
                
                self.results[test_name] = result
                
            except Exception as e:
                logger.error(f"❌ {test_name}: ERROR - {e}")
                self.results[test_name] = False
        
        # Summary
        logger.info(f"\n📊 Test Results Summary")
        logger.info("=" * 30)
        logger.info(f"Passed: {passed}/{total}")
        logger.info(f"Success Rate: {(passed/total)*100:.1f}%")
        
        if passed == total:
            logger.info("\n🎉 ALL TESTS PASSED!")
        else:
            logger.warning(f"\n⚠️  {total - passed} TEST(S) FAILED")
        
        return passed == total
    
    async def test_database_connection(self):
        """Test basic database connection"""
        try:
            result = test_connection()
            if result:
                logger.info("Database connection successful")
                return True
            else:
                logger.error("Database connection failed")
                return False
                
        except Exception as e:
            logger.error(f"Database connection error: {e}")
            return False
    
    async def test_database_health(self):
        """Test database health check"""
        try:
            health = health_check()
            logger.info(f"Database: {health['database']} {health['version']}")
            return True
            
        except Exception as e:
            logger.error(f"Database health check error: {e}")
            return False
    
    async def test_database_schema(self):
        """Test database schema and tables"""
        try:
            from config.pymysql_db import db_manager
            
            # Test database connection and basic table existence
            with db_manager.get_connection() as connection:
                cursor = connection.cursor()
                
                # List of tables that should exist
                tables = [
                    'ohlcv_data',
                    'indicators_data', 
                    'manual_marks',
                    'strategy_log',
                    'strategy_sessions'
                ]
                
                existing_tables = 0
                for table_name in tables:
                    try:
                        cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                        count = cursor.fetchone()[0]
                        logger.info(f"Table {table_name}: {count} records")
                        existing_tables += 1
                    except Exception as table_error:
                        logger.warning(f"Table {table_name}: Not accessible - {table_error}")
                
                # Consider it successful if at least some tables exist
                return existing_tables > 0
                
        except Exception as e:
            logger.error(f"Database schema error: {e}")
            return False
    
    async def test_exchange_apis(self):
        """Test exchange API connections"""
        try:
            results = {}
            
            # Test Binance
            try:
                binance_result = binance_service.test_connection()
                results['binance'] = binance_result
                logger.info(f"Binance API: {'✅ Connected' if binance_result else '❌ Failed'}")
            except Exception as e:
                logger.error(f"Binance API error: {e}")
                results['binance'] = False
            
            # Test MEXC
            try:
                mexc_result = mexc_service.test_connection()
                results['mexc'] = mexc_result
                logger.info(f"MEXC API: {'✅ Connected' if mexc_result else '❌ Failed'}")
            except Exception as e:
                logger.error(f"MEXC API error: {e}")
                results['mexc'] = False
            
            # Consider successful if at least one exchange works
            return any(results.values())
            
        except Exception as e:
            logger.error(f"Exchange API test error: {e}")
            return False
    
    async def test_ohlcv_fetch(self):
        """Test OHLCV data fetching"""
        try:
            logger.info(f"Fetching OHLCV data for {self.test_symbol} {self.test_timeframe}")
            
            # Try to fetch data using the data service
            ohlcv_data = data_service.get_klines(
                exchange=self.test_exchange,
                symbol=self.test_symbol,
                timeframe=self.test_timeframe,
                limit=10
            )
            
            if ohlcv_data and len(ohlcv_data) > 0:
                logger.info(f"Successfully fetched {len(ohlcv_data)} candles")
                
                # Check data structure
                first_candle = ohlcv_data[0]
                required_fields = ['timestamp', 'open', 'high', 'low', 'close', 'volume']
                
                for field in required_fields:
                    if field not in first_candle:
                        logger.error(f"Missing required field: {field}")
                        return False
                
                logger.info("OHLCV data structure is valid")
                return True
            else:
                logger.error("No OHLCV data received")
                return False
                
        except Exception as e:
            logger.error(f"OHLCV fetch error: {e}")
            return False
    
    async def test_settings(self):
        """Test settings configuration"""
        try:
            # Test that settings are loaded
            logger.info(f"App Name: {settings.app.app_name}")
            logger.info(f"Version: {settings.app.version}")
            logger.info(f"Database: {settings.database.database}")
            
            # Test API keys are configured
            binance_configured = bool(settings.binance.api_key)
            mexc_configured = bool(settings.mexc.api_key)
            
            logger.info(f"Binance API configured: {'✅' if binance_configured else '❌'}")
            logger.info(f"MEXC API configured: {'✅' if mexc_configured else '❌'}")
            
            return binance_configured or mexc_configured
            
        except Exception as e:
            logger.error(f"Settings test error: {e}")
            return False


async def main():
    """Main test function"""
    tester = BasicStrategyBuilderTest()
    success = await tester.run_all_tests()
    
    if success:
        logger.info("\n🚀 Strategy Builder is ready to use!")
        logger.info("You can now start the server with: python run.py")
    else:
        logger.error("\n🔧 Some issues found. Please check the logs above.")
    
    return success


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
