#!/usr/bin/env python3
"""
Windows-Compatible Test Script for Strategy Builder
Simple test runner that works on Windows without external dependencies
"""
import sys
import os
import subprocess
import time
import logging
from datetime import datetime

# Configure logging for Windows
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)


class WindowsTestRunner:
    """Simple Windows-compatible test runner"""
    
    def __init__(self):
        self.test_results = {}
        
    def test_python_packages(self):
        """Test if required Python packages are installed"""
        logger.info("Testing Python Packages")
        logger.info("-" * 40)
        
        packages = [
            ("fastapi", "fastapi"),
            ("uvicorn", "uvicorn"),
            ("mysql.connector", "mysql-connector-python"),
            ("pandas", "pandas"),
            ("pandas_ta", "pandas_ta"),
            ("requests", "requests"),
            ("sqlalchemy", "sqlalchemy")
        ]
        
        missing = []
        for import_name, package_name in packages:
            try:
                __import__(import_name)
                logger.info(f"OK: {package_name}")
            except ImportError:
                logger.error(f"MISSING: {package_name}")
                missing.append(package_name)
        
        if missing:
            logger.error(f"Missing packages: {', '.join(missing)}")
            logger.info("Install with: pip install -r requirements.txt")
            return False
        else:
            logger.info("All required packages are installed")
            return True
    
    def test_database_creation(self):
        """Test database creation"""
        logger.info("\nTesting Database Creation")
        logger.info("-" * 40)
        
        try:
            result = subprocess.run([
                sys.executable, "create_database.py"
            ], capture_output=True, text=True, timeout=120)
            
            if result.returncode == 0:
                logger.info("Database creation: SUCCESS")
                logger.info("Output preview:")
                for line in result.stdout.split('\n')[:10]:
                    if line.strip():
                        logger.info(f"  {line}")
                return True
            else:
                logger.error("Database creation: FAILED")
                logger.error(f"Error: {result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            logger.error("Database creation: TIMEOUT")
            return False
        except Exception as e:
            logger.error(f"Database creation: ERROR - {e}")
            return False
    
    def test_backend_features(self):
        """Test backend features"""
        logger.info("\nTesting Backend Features")
        logger.info("-" * 40)
        
        try:
            result = subprocess.run([
                sys.executable, "tests/test_all_features.py"
            ], capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0:
                logger.info("Backend tests: SUCCESS")
                # Show summary from output
                lines = result.stdout.split('\n')
                for line in lines:
                    if 'PASSED' in line or 'FAILED' in line or 'SUCCESS' in line:
                        logger.info(f"  {line}")
                return True
            else:
                logger.error("Backend tests: FAILED")
                logger.error("Error output:")
                for line in result.stderr.split('\n')[:10]:
                    if line.strip():
                        logger.error(f"  {line}")
                return False
                
        except subprocess.TimeoutExpired:
            logger.error("Backend tests: TIMEOUT")
            return False
        except Exception as e:
            logger.error(f"Backend tests: ERROR - {e}")
            return False
    
    def test_server_startup(self):
        """Test if server can start"""
        logger.info("\nTesting Server Startup")
        logger.info("-" * 40)
        
        try:
            # Start server process
            server_process = subprocess.Popen([
                sys.executable, "run.py", "--no-reload"
            ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
            
            # Wait for server to start
            logger.info("Starting server...")
            time.sleep(8)
            
            # Check if server is running
            if server_process.poll() is None:
                logger.info("Server startup: SUCCESS")
                
                # Try to make a simple request
                try:
                    import requests
                    response = requests.get("http://localhost:8000/health", timeout=5)
                    if response.status_code == 200:
                        logger.info("Health check: SUCCESS")
                        health_data = response.json()
                        logger.info(f"  Status: {health_data.get('status')}")
                        logger.info(f"  Version: {health_data.get('version')}")
                        result = True
                    else:
                        logger.warning(f"Health check: HTTP {response.status_code}")
                        result = True  # Server started, just health check issue
                except Exception as e:
                    logger.warning(f"Health check failed: {e}")
                    result = True  # Server started, just request issue
                
                # Stop server
                try:
                    server_process.terminate()
                    server_process.wait(timeout=10)
                    logger.info("Server stopped")
                except:
                    server_process.kill()
                    logger.info("Server killed")
                
                return result
            else:
                logger.error("Server startup: FAILED")
                stdout, stderr = server_process.communicate()
                if stderr:
                    logger.error(f"Server error: {stderr[:500]}")
                return False
                
        except Exception as e:
            logger.error(f"Server test: ERROR - {e}")
            return False
    
    def test_basic_imports(self):
        """Test basic imports from the application"""
        logger.info("\nTesting Application Imports")
        logger.info("-" * 40)
        
        imports_to_test = [
            ("config.settings", "settings"),
            ("config.database", "get_db_session"),
            ("backend.core.technical_indicators", "technical_indicators"),
            ("backend.services.binance_service", "binance_service"),
            ("backend.services.mexc_service", "mexc_service")
        ]
        
        failed_imports = []
        for module_name, import_item in imports_to_test:
            try:
                module = __import__(module_name, fromlist=[import_item])
                getattr(module, import_item)
                logger.info(f"OK: {module_name}.{import_item}")
            except Exception as e:
                logger.error(f"FAILED: {module_name}.{import_item} - {e}")
                failed_imports.append(f"{module_name}.{import_item}")
        
        if failed_imports:
            logger.error(f"Failed imports: {', '.join(failed_imports)}")
            return False
        else:
            logger.info("All application imports successful")
            return True
    
    def run_all_tests(self):
        """Run all tests"""
        logger.info("Strategy Builder Windows Test Suite")
        logger.info("=" * 50)
        logger.info(f"Started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        logger.info("=" * 50)
        
        tests = [
            ("Python Packages", self.test_python_packages),
            ("Application Imports", self.test_basic_imports),
            ("Database Creation", self.test_database_creation),
            ("Backend Features", self.test_backend_features),
            ("Server Startup", self.test_server_startup)
        ]
        
        for test_name, test_func in tests:
            logger.info(f"\n[{test_name}]")
            try:
                result = test_func()
                self.test_results[test_name] = result
                
                if result:
                    logger.info(f"RESULT: {test_name} - PASSED")
                else:
                    logger.error(f"RESULT: {test_name} - FAILED")
                    
            except Exception as e:
                logger.error(f"RESULT: {test_name} - ERROR: {e}")
                self.test_results[test_name] = False
        
        # Print summary
        self.print_summary()
        
        # Return overall success
        return all(self.test_results.values())
    
    def print_summary(self):
        """Print test summary"""
        logger.info("\n" + "=" * 50)
        logger.info("TEST SUMMARY")
        logger.info("=" * 50)
        
        total = len(self.test_results)
        passed = sum(1 for result in self.test_results.values() if result)
        failed = total - passed
        
        logger.info(f"Total Tests: {total}")
        logger.info(f"Passed: {passed}")
        logger.info(f"Failed: {failed}")
        logger.info(f"Success Rate: {(passed/total)*100:.1f}%")
        
        logger.info("\nDetailed Results:")
        for test_name, result in self.test_results.items():
            status = "PASSED" if result else "FAILED"
            logger.info(f"  {test_name}: {status}")
        
        if passed == total:
            logger.info("\nALL TESTS PASSED!")
            logger.info("Strategy Builder is ready to use.")
            logger.info("\nNext steps:")
            logger.info("1. Run: python run.py")
            logger.info("2. Open: http://localhost:8000")
        else:
            logger.info(f"\n{failed} TEST(S) FAILED!")
            logger.info("Please fix the issues before using Strategy Builder.")
            
            if not self.test_results.get("Python Packages", True):
                logger.info("- Install missing packages: pip install -r requirements.txt")
            if not self.test_results.get("Database Creation", True):
                logger.info("- Check MySQL connection and credentials")
            if not self.test_results.get("Backend Features", True):
                logger.info("- Review backend test output for specific issues")


def main():
    """Main function"""
    print("Strategy Builder - Windows Test Suite")
    print("This test suite is designed to work on Windows without external dependencies.")
    print("")
    
    # Change to the correct directory
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)
    
    runner = WindowsTestRunner()
    success = runner.run_all_tests()
    
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
