# Strategy Builder Environment Configuration
# Copy this file to .env and update with your actual values

# Application Settings
DEBUG=true
HOST=127.0.0.1
PORT=8000

# Database Configuration
DB_HOST=localhost
DB_PORT=3306
DB_USERNAME=root
DB_PASSWORD=your_mysql_password
DB_DATABASE=strategy_builder

# Binance API Configuration
BINANCE_API_KEY=your_binance_api_key
BINANCE_API_SECRET=your_binance_api_secret
BINANCE_TESTNET=true
BINANCE_BASE_URL=https://testnet.binancefuture.com

# MEXC API Configuration
MEXC_API_KEY=your_mexc_api_key
MEXC_API_SECRET=your_mexc_api_secret
MEXC_BASE_URL=https://api.mexc.com
