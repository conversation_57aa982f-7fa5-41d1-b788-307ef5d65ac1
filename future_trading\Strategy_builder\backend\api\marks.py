"""
Manual Marks API endpoints for entry/exit marking
"""
from fastapi import APIRouter, HTTPException, Depends
from sqlalchemy.orm import Session
from typing import List, Dict, Any, Optional
from pydantic import BaseModel
from datetime import datetime
import logging

from config.database import get_db
from backend.database.operations import MarkOperations, OHLCVOperations, StrategyOperations, SessionOperations
from backend.core.technical_indicators import calculate_profit_pnl

logger = logging.getLogger(__name__)
router = APIRouter()


class EntryMarkRequest(BaseModel):
    symbol: str
    timeframe: str
    timestamp: str
    price: float
    entry_side: str  # 'buy' or 'sell'
    indicator_snapshot: Optional[Dict[str, Any]] = {}
    ohlcv_snapshot: Optional[Dict[str, Any]] = {}
    session_id: str


class ExitMarkRequest(BaseModel):
    symbol: str
    timeframe: str
    timestamp: str
    price: float
    linked_trade_id: int  # ID of the entry mark
    indicator_snapshot: Optional[Dict[str, Any]] = {}
    ohlcv_snapshot: Optional[Dict[str, Any]] = {}
    session_id: str


class MarkResponse(BaseModel):
    mark_id: int
    symbol: str
    timeframe: str
    mark_type: str
    timestamp: str
    price: float
    session_id: str


@router.post("/entry")
async def create_entry_mark(
    request: EntryMarkRequest,
    db: Session = Depends(get_db)
) -> MarkResponse:
    """
    Create an entry mark
    
    Args:
        request: Entry mark request
        
    Returns:
        Created entry mark details
    """
    try:
        # Validate entry side
        if request.entry_side.lower() not in ['buy', 'sell']:
            raise HTTPException(status_code=400, detail="entry_side must be 'buy' or 'sell'")
        
        # Parse timestamp
        try:
            timestamp = datetime.fromisoformat(request.timestamp.replace('Z', '+00:00'))
        except ValueError:
            raise HTTPException(status_code=400, detail="Invalid timestamp format")
        
        # Validate that OHLCV data exists for this timestamp
        ohlcv_data = OHLCVOperations.get_ohlcv_data(
            db=db,
            symbol=request.symbol,
            timeframe=request.timeframe,
            start_time=timestamp,
            end_time=timestamp,
            limit=1
        )
        
        if not ohlcv_data:
            logger.warning(f"No OHLCV data found for {request.symbol} at {timestamp}")
        
        # Create entry mark
        mark_id = MarkOperations.create_entry_mark(
            db=db,
            symbol=request.symbol,
            timeframe=request.timeframe,
            timestamp=timestamp,
            price=request.price,
            entry_side=request.entry_side.lower(),
            indicator_snapshot=request.indicator_snapshot or {},
            ohlcv_snapshot=request.ohlcv_snapshot or {},
            session_id=request.session_id
        )
        
        logger.info(f"Created entry mark {mark_id} for {request.symbol} at {timestamp}")
        
        return MarkResponse(
            mark_id=mark_id,
            symbol=request.symbol,
            timeframe=request.timeframe,
            mark_type="entry",
            timestamp=request.timestamp,
            price=request.price,
            session_id=request.session_id
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating entry mark: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/exit")
async def create_exit_mark(
    request: ExitMarkRequest,
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    Create an exit mark and complete the trade
    
    Args:
        request: Exit mark request
        
    Returns:
        Created exit mark details and trade summary
    """
    try:
        # Parse timestamp
        try:
            timestamp = datetime.fromisoformat(request.timestamp.replace('Z', '+00:00'))
        except ValueError:
            raise HTTPException(status_code=400, detail="Invalid timestamp format")
        
        # Get the entry mark
        entry_marks = MarkOperations.get_marks_by_session(db, request.session_id)
        entry_mark = None
        
        for mark in entry_marks:
            if mark['id'] == request.linked_trade_id and mark['mark_type'] == 'entry':
                entry_mark = mark
                break
        
        if not entry_mark:
            raise HTTPException(status_code=404, detail="Entry mark not found")
        
        # Create exit mark
        exit_mark_id = MarkOperations.create_exit_mark(
            db=db,
            symbol=request.symbol,
            timeframe=request.timeframe,
            timestamp=timestamp,
            price=request.price,
            indicator_snapshot=request.indicator_snapshot or {},
            ohlcv_snapshot=request.ohlcv_snapshot or {},
            linked_trade_id=request.linked_trade_id,
            session_id=request.session_id
        )
        
        # Calculate profit/loss
        profit_data = calculate_profit_pnl(
            entry_price=entry_mark['price'],
            exit_price=request.price,
            entry_side=entry_mark['entry_side']
        )
        
        # Create strategy log entry
        from backend.database.models import ManualMarks
        
        # Get the actual entry and exit mark objects
        entry_mark_obj = db.query(ManualMarks).filter(ManualMarks.id == request.linked_trade_id).first()
        exit_mark_obj = db.query(ManualMarks).filter(ManualMarks.id == exit_mark_id).first()
        
        if entry_mark_obj and exit_mark_obj:
            strategy_id = StrategyOperations.create_strategy_log(
                db=db,
                entry_mark=entry_mark_obj,
                exit_mark=exit_mark_obj,
                profit_pct=profit_data['profit_pct'],
                profit_absolute=profit_data['profit_absolute']
            )
            
            # Update session statistics
            SessionOperations.update_session_stats(db, request.session_id)
            
            logger.info(f"Completed trade: Entry {request.linked_trade_id} -> Exit {exit_mark_id}, Profit: {profit_data['profit_pct']:.2f}%")
        
        return {
            "exit_mark": MarkResponse(
                mark_id=exit_mark_id,
                symbol=request.symbol,
                timeframe=request.timeframe,
                mark_type="exit",
                timestamp=request.timestamp,
                price=request.price,
                session_id=request.session_id
            ),
            "trade_summary": {
                "entry_id": request.linked_trade_id,
                "exit_id": exit_mark_id,
                "entry_price": entry_mark['price'],
                "exit_price": request.price,
                "entry_side": entry_mark['entry_side'],
                "profit_pct": profit_data['profit_pct'],
                "profit_absolute": profit_data['profit_absolute'],
                "strategy_id": strategy_id if 'strategy_id' in locals() else None
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating exit mark: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/session/{session_id}")
async def get_session_marks(
    session_id: str,
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """Get all marks for a session"""
    try:
        marks = MarkOperations.get_marks_by_session(db, session_id)
        
        # Separate entry and exit marks
        entry_marks = [mark for mark in marks if mark['mark_type'] == 'entry']
        exit_marks = [mark for mark in marks if mark['mark_type'] == 'exit']
        
        return {
            "session_id": session_id,
            "total_marks": len(marks),
            "entry_marks": entry_marks,
            "exit_marks": exit_marks,
            "marks": marks
        }
        
    except Exception as e:
        logger.error(f"Error getting session marks: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.delete("/mark/{mark_id}")
async def delete_mark(
    mark_id: int,
    db: Session = Depends(get_db)
) -> Dict[str, str]:
    """Delete a mark (and associated trade if it's an entry mark)"""
    try:
        # This would implement mark deletion logic
        # For now, return a placeholder
        return {
            "message": f"Mark {mark_id} deletion not yet implemented",
            "status": "pending"
        }
        
    except Exception as e:
        logger.error(f"Error deleting mark: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/open-entries/{session_id}")
async def get_open_entries(
    session_id: str,
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """Get entry marks that don't have corresponding exit marks"""
    try:
        marks = MarkOperations.get_marks_by_session(db, session_id)
        
        # Find entry marks without exits
        entry_marks = [mark for mark in marks if mark['mark_type'] == 'entry']
        exit_marks = [mark for mark in marks if mark['mark_type'] == 'exit']
        
        # Get linked trade IDs from exit marks
        linked_ids = {mark['linked_trade_id'] for mark in exit_marks if mark['linked_trade_id']}
        
        # Find open entries
        open_entries = [mark for mark in entry_marks if mark['id'] not in linked_ids]
        
        return {
            "session_id": session_id,
            "open_entries": open_entries,
            "count": len(open_entries)
        }
        
    except Exception as e:
        logger.error(f"Error getting open entries: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")
