"""
Manual Marks API endpoints for entry/exit marking
Simplified version using PyMySQL operations
"""
from fastapi import APIRouter, HTTPException
from typing import List, Dict, Any, Optional
from pydantic import BaseModel
from datetime import datetime
import logging

from backend.database.operations_pymysql import ManualMarksOperations, OHLCVOperations, StrategySessionsOperations

logger = logging.getLogger(__name__)
router = APIRouter()


class EntryMarkRequest(BaseModel):
    symbol: str
    timeframe: str
    timestamp: str  # ISO format
    price: float
    entry_side: str  # 'buy' or 'sell'
    session_id: str
    indicator_snapshot: Optional[Dict[str, Any]] = {}
    ohlcv_snapshot: Optional[Dict[str, Any]] = {}


class ExitMarkRequest(BaseModel):
    symbol: str
    timeframe: str
    timestamp: str  # ISO format
    price: float
    linked_trade_id: str  # ID of the entry mark
    session_id: str
    indicator_snapshot: Optional[Dict[str, Any]] = {}
    ohlcv_snapshot: Optional[Dict[str, Any]] = {}


class MarkResponse(BaseModel):
    id: str
    symbol: str
    timeframe: str
    timestamp: str
    price: float
    mark_type: str
    entry_side: Optional[str] = None
    linked_trade_id: Optional[str] = None
    session_id: str
    created_at: str


@router.post("/entry")
async def create_entry_mark(request: EntryMarkRequest) -> MarkResponse:
    """Create an entry mark"""
    try:
        # Parse timestamp
        try:
            timestamp = datetime.fromisoformat(request.timestamp.replace('Z', '+00:00'))
        except ValueError:
            raise HTTPException(status_code=400, detail="Invalid timestamp format")
        
        # Create entry mark
        mark_data = {
            'timestamp': timestamp,
            'price': request.price,
            'mark_type': 'entry',
            'entry_side': request.entry_side.lower(),
            'session_id': request.session_id,
            'indicator_snapshot': request.indicator_snapshot or {},
            'ohlcv_snapshot': request.ohlcv_snapshot or {}
        }
        
        mark_id = ManualMarksOperations.create_manual_mark(
            symbol=request.symbol,
            timeframe=request.timeframe,
            mark_data=mark_data
        )
        
        logger.info(f"Created entry mark {mark_id} for {request.symbol} at {timestamp}")
        
        return MarkResponse(
            id=mark_id,
            symbol=request.symbol,
            timeframe=request.timeframe,
            timestamp=request.timestamp,
            price=request.price,
            mark_type="entry",
            entry_side=request.entry_side.lower(),
            session_id=request.session_id,
            created_at=datetime.now().isoformat()
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating entry mark: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/exit")
async def create_exit_mark(request: ExitMarkRequest) -> Dict[str, Any]:
    """Create an exit mark and complete the trade"""
    try:
        # Parse timestamp
        try:
            timestamp = datetime.fromisoformat(request.timestamp.replace('Z', '+00:00'))
        except ValueError:
            raise HTTPException(status_code=400, detail="Invalid timestamp format")
        
        # Create exit mark
        mark_data = {
            'timestamp': timestamp,
            'price': request.price,
            'mark_type': 'exit',
            'linked_trade_id': request.linked_trade_id,
            'session_id': request.session_id,
            'indicator_snapshot': request.indicator_snapshot or {},
            'ohlcv_snapshot': request.ohlcv_snapshot or {}
        }
        
        exit_mark_id = ManualMarksOperations.create_manual_mark(
            symbol=request.symbol,
            timeframe=request.timeframe,
            mark_data=mark_data
        )
        
        logger.info(f"Created exit mark {exit_mark_id} for {request.symbol} at {timestamp}")
        
        # For now, return basic response without complex profit calculations
        return {
            "exit_mark": MarkResponse(
                id=exit_mark_id,
                symbol=request.symbol,
                timeframe=request.timeframe,
                timestamp=request.timestamp,
                price=request.price,
                mark_type="exit",
                linked_trade_id=request.linked_trade_id,
                session_id=request.session_id,
                created_at=datetime.now().isoformat()
            ),
            "trade_completed": True,
            "message": "Exit mark created successfully"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating exit mark: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/session/{session_id}")
async def get_session_marks(session_id: str) -> Dict[str, Any]:
    """Get all marks for a session"""
    try:
        marks = ManualMarksOperations.get_marks_for_chart(
            symbol="",  # Will get all symbols for this session
            timeframe="",  # Will get all timeframes for this session
            session_id=session_id
        )
        
        # Separate entry and exit marks
        entry_marks = [mark for mark in marks if mark['mark_type'] == 'entry']
        exit_marks = [mark for mark in marks if mark['mark_type'] == 'exit']
        
        return {
            "session_id": session_id,
            "total_marks": len(marks),
            "entry_marks": entry_marks,
            "exit_marks": exit_marks,
            "marks": marks
        }
        
    except Exception as e:
        logger.error(f"Error getting session marks: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.delete("/mark/{mark_id}")
async def delete_mark(mark_id: int) -> Dict[str, str]:
    """Delete a mark (placeholder implementation)"""
    try:
        return {
            "message": f"Mark deletion not yet implemented for mark {mark_id}",
            "status": "placeholder"
        }
        
    except Exception as e:
        logger.error(f"Error deleting mark: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/open-entries/{session_id}")
async def get_open_entries(session_id: str) -> Dict[str, Any]:
    """Get entry marks that don't have corresponding exit marks"""
    try:
        marks = ManualMarksOperations.get_marks_for_chart(
            symbol="",  # Will get all symbols for this session
            timeframe="",  # Will get all timeframes for this session
            session_id=session_id
        )
        
        # Find entry marks without exits
        entry_marks = [mark for mark in marks if mark['mark_type'] == 'entry']
        exit_marks = [mark for mark in marks if mark['mark_type'] == 'exit']
        
        # Find entries without corresponding exits
        open_entries = []
        for entry in entry_marks:
            has_exit = any(
                exit_mark.get('linked_trade_id') == entry['id'] 
                for exit_mark in exit_marks
            )
            if not has_exit:
                open_entries.append(entry)
        
        return {
            "session_id": session_id,
            "open_entries": open_entries,
            "count": len(open_entries)
        }
        
    except Exception as e:
        logger.error(f"Error getting open entries: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/health")
async def marks_health() -> Dict[str, str]:
    """Marks API health check"""
    return {
        "status": "healthy",
        "service": "marks_api",
        "timestamp": datetime.now().isoformat()
    }
