#!/usr/bin/env python3
"""
Comprehensive Test Runner for Strategy Builder
Runs database setup, backend tests, API tests, and frontend tests
"""
import os
import sys
import subprocess
import time
import logging
import argparse
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class TestRunner:
    """Comprehensive test runner for Strategy Builder"""
    
    def __init__(self):
        self.test_results = {}
        self.server_process = None
        
    def run_command(self, command, description, timeout=60):
        """Run a command and return success status"""
        try:
            logger.info(f"Running: {description}")
            logger.debug(f"Command: {command}")
            
            result = subprocess.run(
                command,
                shell=True,
                capture_output=True,
                text=True,
                timeout=timeout
            )
            
            if result.returncode == 0:
                logger.info(f"✅ {description}: SUCCESS")
                if result.stdout:
                    logger.debug(f"Output: {result.stdout[:500]}...")
                return True
            else:
                logger.error(f"❌ {description}: FAILED")
                if result.stderr:
                    logger.error(f"Error: {result.stderr[:500]}...")
                return False
                
        except subprocess.TimeoutExpired:
            logger.error(f"❌ {description}: TIMEOUT")
            return False
        except Exception as e:
            logger.error(f"❌ {description}: ERROR - {e}")
            return False
    
    def check_dependencies(self):
        """Check if all required dependencies are available"""
        logger.info("🔍 Checking Dependencies")
        logger.info("-" * 40)
        
        dependencies = [
            ("python", "Python interpreter"),
            ("mysql", "MySQL server"),
        ]
        
        missing = []
        for cmd, desc in dependencies:
            if not self.run_command(f"which {cmd}", f"Check {desc}", timeout=5):
                missing.append(desc)
        
        # Check Python packages
        python_packages = [
            "fastapi", "uvicorn", "mysql-connector-python", 
            "pandas", "pandas_ta", "requests"
        ]
        
        for package in python_packages:
            if not self.run_command(f"python -c 'import {package.replace('-', '_')}'", f"Check {package}", timeout=5):
                missing.append(f"Python package: {package}")
        
        if missing:
            logger.error(f"❌ Missing dependencies: {', '.join(missing)}")
            logger.info("Run: pip install -r requirements.txt")
            return False
        else:
            logger.info("✅ All dependencies available")
            return True
    
    def setup_database(self):
        """Setup database"""
        logger.info("\n🗄️ Setting Up Database")
        logger.info("-" * 40)
        
        return self.run_command(
            "python create_database.py",
            "Database setup",
            timeout=120
        )
    
    def run_backend_tests(self):
        """Run comprehensive backend tests"""
        logger.info("\n🔧 Running Backend Tests")
        logger.info("-" * 40)
        
        return self.run_command(
            "python tests/test_all_features.py",
            "Backend comprehensive tests",
            timeout=300
        )
    
    def start_server(self):
        """Start the FastAPI server"""
        logger.info("\n🚀 Starting Server")
        logger.info("-" * 40)
        
        try:
            # Start server in background
            self.server_process = subprocess.Popen(
                ["python", "run.py", "--no-reload"],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            # Wait for server to start
            logger.info("Waiting for server to start...")
            time.sleep(10)
            
            # Check if server is running
            if self.server_process.poll() is None:
                logger.info("✅ Server started successfully")
                return True
            else:
                logger.error("❌ Server failed to start")
                return False
                
        except Exception as e:
            logger.error(f"❌ Error starting server: {e}")
            return False
    
    def stop_server(self):
        """Stop the FastAPI server"""
        if self.server_process:
            try:
                self.server_process.terminate()
                self.server_process.wait(timeout=10)
                logger.info("✅ Server stopped")
            except:
                self.server_process.kill()
                logger.info("🔥 Server killed")
    
    def run_api_tests(self):
        """Run API endpoint tests"""
        logger.info("\n🌐 Running API Tests")
        logger.info("-" * 40)
        
        return self.run_command(
            "python tests/test_api_endpoints.py",
            "API endpoint tests",
            timeout=180
        )
    
    def run_frontend_tests(self):
        """Run frontend tests"""
        logger.info("\n🖥️ Running Frontend Tests")
        logger.info("-" * 40)
        
        # Check if Chrome is available
        if not self.run_command("which google-chrome || which chromium-browser", "Check Chrome browser", timeout=5):
            logger.warning("⚠️ Chrome browser not found, skipping frontend tests")
            return True
        
        return self.run_command(
            "python tests/test_frontend.py",
            "Frontend tests",
            timeout=300
        )
    
    def run_performance_tests(self):
        """Run performance tests"""
        logger.info("\n⚡ Running Performance Tests")
        logger.info("-" * 40)
        
        # Simple performance test - load data and calculate indicators
        performance_script = """
import time
import sys
sys.path.append('.')
from backend.services.mexc_service import data_service
from backend.core.technical_indicators import technical_indicators

start_time = time.time()

# Test data fetching
ohlcv_data = data_service.get_klines('binance', 'BTCUSDT', '1h', limit=1000)
fetch_time = time.time() - start_time

# Test indicator calculation
start_time = time.time()
indicators_config = [
    {'name': 'RSI', 'params': {'length': 14}},
    {'name': 'MACD', 'params': {'fast': 12, 'slow': 26, 'signal': 9}},
    {'name': 'EMA', 'params': {'length': 20}}
]
indicators = technical_indicators.calculate_indicators(ohlcv_data, indicators_config)
calc_time = time.time() - start_time

print(f"Fetch time: {fetch_time:.2f}s")
print(f"Calculation time: {calc_time:.2f}s")
print(f"Candles processed: {len(ohlcv_data)}")
print(f"Indicators calculated: {len(indicators)}")

# Performance thresholds
if fetch_time > 10:
    print("WARNING: Data fetching is slow")
    sys.exit(1)
if calc_time > 5:
    print("WARNING: Indicator calculation is slow")
    sys.exit(1)

print("Performance tests passed")
"""
        
        with open("temp_performance_test.py", "w") as f:
            f.write(performance_script)
        
        try:
            result = self.run_command(
                "python temp_performance_test.py",
                "Performance tests",
                timeout=60
            )
        finally:
            if os.path.exists("temp_performance_test.py"):
                os.remove("temp_performance_test.py")
        
        return result
    
    def generate_test_report(self):
        """Generate comprehensive test report"""
        logger.info("\n📊 Generating Test Report")
        logger.info("-" * 40)
        
        report = f"""
# Strategy Builder Test Report
Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## Test Results Summary
"""
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result)
        
        report += f"""
- Total Test Suites: {total_tests}
- Passed: {passed_tests}
- Failed: {total_tests - passed_tests}
- Success Rate: {(passed_tests/total_tests)*100:.1f}%

## Detailed Results
"""
        
        for test_name, result in self.test_results.items():
            status = "✅ PASSED" if result else "❌ FAILED"
            report += f"- {test_name}: {status}\n"
        
        report += f"""
## System Information
- Python Version: {sys.version.split()[0]}
- Operating System: {os.name}
- Working Directory: {os.getcwd()}

## Next Steps
"""
        
        if passed_tests == total_tests:
            report += """
🎉 All tests passed! Strategy Builder is ready for production use.

Recommended actions:
1. Deploy to production environment
2. Configure monitoring and logging
3. Set up backup procedures
4. Train users on the interface
"""
        else:
            report += """
⚠️ Some tests failed. Review and fix issues before production deployment.

Recommended actions:
1. Review failed test logs
2. Fix identified issues
3. Re-run tests
4. Consider additional testing
"""
        
        # Save report
        with open("test_report.md", "w") as f:
            f.write(report)
        
        logger.info("✅ Test report saved to test_report.md")
        return report
    
    def run_all_tests(self, skip_frontend=False, skip_performance=False):
        """Run all test suites"""
        logger.info("🚀 Strategy Builder Comprehensive Test Suite")
        logger.info("=" * 60)
        logger.info(f"Started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        logger.info("=" * 60)
        
        test_suites = [
            ("Dependencies Check", self.check_dependencies),
            ("Database Setup", self.setup_database),
            ("Backend Tests", self.run_backend_tests),
            ("Server Startup", self.start_server),
            ("API Tests", self.run_api_tests),
        ]
        
        if not skip_frontend:
            test_suites.append(("Frontend Tests", self.run_frontend_tests))
        
        if not skip_performance:
            test_suites.append(("Performance Tests", self.run_performance_tests))
        
        try:
            for test_name, test_func in test_suites:
                result = test_func()
                self.test_results[test_name] = result
                
                if not result and test_name in ["Dependencies Check", "Database Setup"]:
                    logger.error(f"Critical test failed: {test_name}")
                    logger.error("Stopping test execution")
                    break
        
        finally:
            # Always stop server
            self.stop_server()
        
        # Generate report
        self.generate_test_report()
        
        # Print final summary
        logger.info("\n" + "=" * 60)
        logger.info("🏁 FINAL TEST SUMMARY")
        logger.info("=" * 60)
        
        passed = sum(1 for result in self.test_results.values() if result)
        total = len(self.test_results)
        
        logger.info(f"Total Test Suites: {total}")
        logger.info(f"✅ Passed: {passed}")
        logger.info(f"❌ Failed: {total - passed}")
        logger.info(f"Success Rate: {(passed/total)*100:.1f}%")
        
        if passed == total:
            logger.info("\n🎉 ALL TESTS PASSED! Strategy Builder is ready!")
        elif passed > total * 0.8:
            logger.info("\n⚠️ Most tests passed. Minor issues to address.")
        else:
            logger.info("\n🚨 Multiple failures. Significant issues to fix.")
        
        return passed == total


def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='Strategy Builder Test Runner')
    parser.add_argument('--skip-frontend', action='store_true', help='Skip frontend tests')
    parser.add_argument('--skip-performance', action='store_true', help='Skip performance tests')
    parser.add_argument('--quick', action='store_true', help='Skip frontend and performance tests')
    
    args = parser.parse_args()
    
    if args.quick:
        args.skip_frontend = True
        args.skip_performance = True
    
    runner = TestRunner()
    success = runner.run_all_tests(
        skip_frontend=args.skip_frontend,
        skip_performance=args.skip_performance
    )
    
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
