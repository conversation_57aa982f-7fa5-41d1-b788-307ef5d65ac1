# Strategy Builder - Windows Setup Guide

## Quick Windows Setup

### 1. Prerequisites
- Python 3.8+ installed
- MySQL Server running
- Command Prompt or PowerShell

### 2. Installation Steps

**Step 1: Install Dependencies**
```cmd
pip install -r requirements.txt
```

**Step 2: Check Requirements**
```cmd
python check_requirements.py
```

**Step 3: Create Database**
```cmd
python create_database.py
```

**Step 4: Run Tests**
```cmd
python test_windows.py
```

**Step 5: Start Application**
```cmd
python run.py
```

**Step 6: Open Browser**
```
http://localhost:8000
```

## Windows-Specific Scripts

### `check_requirements.py`
- Checks Python version
- Verifies all packages are installed
- Tests database connection
- Validates file structure

### `test_windows.py`
- Windows-compatible test runner
- No external dependencies
- Tests all core functionality
- Provides clear pass/fail results

### `create_database.py`
- Creates the `strategy_buider` database
- Sets up all tables and indexes
- Uses your configured credentials
- Verifies setup completion

## Expected Output

### Requirements Check
```
Strategy Builder Requirements Check
==================================================
Checking Python Version
------------------------------
Python version: 3.12.0
Python version: OK

Checking Required Packages
------------------------------
fastapi: OK
uvicorn: OK
mysql-connector-python: OK
pandas: OK
pandas_ta: OK
requests: OK
sqlalchemy: OK
pydantic: OK

All packages: OK

Checking File Structure
------------------------------
requirements.txt: OK
create_database.py: OK
run.py: OK
config/settings.py: OK
...

Checking Database Configuration
------------------------------
MySQL connection: OK
MySQL version: 8.0.35
Database 'strategy_buider': EXISTS

==================================================
REQUIREMENTS CHECK SUMMARY
==================================================
Total Checks: 4
Passed: 4
Failed: 0
  Python Version: PASSED
  Required Packages: PASSED
  File Structure: PASSED
  Database Configuration: PASSED

ALL REQUIREMENTS MET!
```

### Windows Tests
```
Strategy Builder Windows Test Suite
==================================================
Started: 2025-07-27 13:45:00
==================================================

[Python Packages]
Testing Python Packages
----------------------------------------
OK: fastapi
OK: uvicorn
OK: mysql-connector-python
OK: pandas
OK: pandas_ta
OK: requests
OK: sqlalchemy
All required packages are installed
RESULT: Python Packages - PASSED

[Application Imports]
Testing Application Imports
----------------------------------------
OK: config.settings.settings
OK: config.database.get_db_session
OK: backend.core.technical_indicators.technical_indicators
OK: backend.services.binance_service.binance_service
OK: backend.services.mexc_service.mexc_service
All application imports successful
RESULT: Application Imports - PASSED

[Database Creation]
Testing Database Creation
----------------------------------------
Database creation: SUCCESS
RESULT: Database Creation - PASSED

[Backend Features]
Testing Backend Features
----------------------------------------
Backend tests: SUCCESS
RESULT: Backend Features - PASSED

[Server Startup]
Testing Server Startup
----------------------------------------
Starting server...
Server startup: SUCCESS
Health check: SUCCESS
  Status: healthy
  Version: 1.0.0
Server stopped
RESULT: Server Startup - PASSED

==================================================
TEST SUMMARY
==================================================
Total Tests: 5
Passed: 5
Failed: 0
Success Rate: 100.0%

Detailed Results:
  Python Packages: PASSED
  Application Imports: PASSED
  Database Creation: PASSED
  Backend Features: PASSED
  Server Startup: PASSED

ALL TESTS PASSED!
Strategy Builder is ready to use.

Next steps:
1. Run: python run.py
2. Open: http://localhost:8000
```

## Troubleshooting Windows Issues

### Common Problems

**1. Unicode/Encoding Errors**
- Use `test_windows.py` instead of `run_tests.py`
- The Windows scripts handle encoding properly

**2. MySQL Connection Issues**
```cmd
# Check if MySQL is running
net start mysql

# Test connection
mysql -u root -p@Oppa121089 -e "SHOW DATABASES;"
```

**3. Package Installation Issues**
```cmd
# Upgrade pip first
python -m pip install --upgrade pip

# Install packages
pip install -r requirements.txt

# If still issues, try one by one
pip install fastapi uvicorn mysql-connector-python pandas pandas_ta requests sqlalchemy pydantic
```

**4. Port Already in Use**
```cmd
# Check what's using port 8000
netstat -an | findstr :8000

# Kill Python processes
taskkill /f /im python.exe

# Use different port
python run.py --port 8001
```

**5. Path Issues**
- Make sure you're in the `Strategy_builder` directory
- Use full paths if needed
- Check that Python is in your PATH

### Success Indicators

✅ **Requirements Check**: All 4 checks pass
✅ **Windows Tests**: All 5 tests pass  
✅ **Database**: Tables created successfully
✅ **Server**: Starts without errors
✅ **Health Check**: Returns status "healthy"

## Next Steps After Success

1. **Start the application**: `python run.py`
2. **Open browser**: http://localhost:8000
3. **Create a session**: Click "New Session"
4. **Load data**: Enter BTCUSDT, select 1h, click "Load Data"
5. **Add indicators**: Check RSI and MACD, click "Calculate Indicators"
6. **Mark trades**: Click on chart, then "Mark Entry (Buy)", click again, "Mark Exit"

## Getting Help

If you encounter issues:

1. **Check the logs**: Look for error messages in the console
2. **Run requirements check**: `python check_requirements.py`
3. **Try Windows tests**: `python test_windows.py`
4. **Check database**: `python create_database.py`
5. **Review the main SETUP_GUIDE.md** for detailed troubleshooting

The Windows-specific scripts are designed to work reliably on Windows systems and provide clear feedback about any issues.
