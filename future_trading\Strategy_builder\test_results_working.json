[{"test": "GET /api/health", "status": "✅ PASS", "success": true, "details": "Service: strategy_builder_api, DB: connected", "timestamp": "2025-07-27T22:37:01.734183"}, {"test": "GET /api/config", "status": "✅ PASS", "success": true, "details": "BB length: 4, Total indicators: 6", "timestamp": "2025-07-27T22:37:01.749186"}, {"test": "Bollinger Bands Length Validation", "status": "✅ PASS", "success": true, "details": "BB default length is correctly set to 4", "timestamp": "2025-07-27T22:37:01.751184"}, {"test": "GET /api/exchanges", "status": "✅ PASS", "success": true, "details": "Exchanges: binance, mexc", "timestamp": "2025-07-27T22:37:01.755184"}, {"test": "GET /api/symbols/binance", "status": "✅ PASS", "success": true, "details": "Found 10 symbols", "timestamp": "2025-07-27T22:37:01.761184"}, {"test": "GET /api/symbols/mexc", "status": "✅ PASS", "success": true, "details": "Found 10 symbols", "timestamp": "2025-07-27T22:37:01.767184"}, {"test": "GET / (Frontend)", "status": "✅ PASS", "success": true, "details": "Frontend page loads successfully", "timestamp": "2025-07-27T22:37:01.776185"}, {"test": "GET /docs (API Docs)", "status": "✅ PASS", "success": true, "details": "API documentation accessible", "timestamp": "2025-07-27T22:37:01.780182"}, {"test": "BB Backend Configuration", "status": "✅ PASS", "success": true, "details": "✅ Backend: length=4, std=2", "timestamp": "2025-07-27T22:37:01.786183"}, {"test": "BB Support Check", "status": "✅ PASS", "success": true, "details": "✅ BB is in supported indicators", "timestamp": "2025-07-27T22:37:01.786183"}]