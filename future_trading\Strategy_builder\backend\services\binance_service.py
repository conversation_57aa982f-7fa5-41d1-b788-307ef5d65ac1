"""
Binance API Service for Strategy Builder
"""
import requests
import pandas as pd
from datetime import datetime, timedelta
from typing import List, Dict, Optional
import logging
import time

from config.settings import settings

logger = logging.getLogger(__name__)


class BinanceService:
    """Binance API service for fetching OHLCV data"""
    
    def __init__(self):
        self.base_url = settings.binance.base_url
        self.futures_base_url = settings.binance.base_url
        self.api_key = settings.binance.api_key
        self.api_secret = settings.binance.api_secret
        self.session = requests.Session()

        # Add API key to session headers if available
        if self.api_key:
            self.session.headers.update({
                'X-MBX-APIKEY': self.api_key
            })
        
        # Timeframe mapping
        self.timeframe_map = {
            '1m': '1m',
            '3m': '3m', 
            '5m': '5m',
            '15m': '15m',
            '30m': '30m',
            '1h': '1h',
            '2h': '2h',
            '4h': '4h',
            '1d': '1d',
            '1w': '1w'
        }
    
    def get_klines(self, symbol: str, timeframe: str, limit: int = 500,
                   start_time: Optional[datetime] = None,
                   end_time: Optional[datetime] = None) -> List[Dict]:
        """
        Fetch klines (OHLCV) data from Binance
        
        Args:
            symbol: Trading symbol (e.g., 'BTCUSDT')
            timeframe: Timeframe (e.g., '1h', '1d')
            limit: Number of candles to fetch (max 1500)
            start_time: Start time for data
            end_time: End time for data
            
        Returns:
            List of OHLCV dictionaries
        """
        try:
            # Validate inputs
            if timeframe not in self.timeframe_map:
                raise ValueError(f"Unsupported timeframe: {timeframe}")
            
            if limit > 1500:
                limit = 1500
                logger.warning("Limit reduced to 1500 (Binance maximum)")
            
            # Prepare parameters
            params = {
                'symbol': symbol.upper(),
                'interval': self.timeframe_map[timeframe],
                'limit': limit
            }
            
            if start_time:
                params['startTime'] = int(start_time.timestamp() * 1000)
            if end_time:
                params['endTime'] = int(end_time.timestamp() * 1000)
            
            # Make API request
            url = f"{self.futures_base_url}/fapi/v1/klines"
            response = self.session.get(url, params=params)
            response.raise_for_status()
            
            data = response.json()
            
            # Convert to OHLCV format
            ohlcv_data = []
            for kline in data:
                ohlcv_data.append({
                    'timestamp': datetime.fromtimestamp(kline[0] / 1000),
                    'open': float(kline[1]),
                    'high': float(kline[2]),
                    'low': float(kline[3]),
                    'close': float(kline[4]),
                    'volume': float(kline[5])
                })
            
            logger.info(f"Fetched {len(ohlcv_data)} candles for {symbol} {timeframe}")
            return ohlcv_data
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Binance API request error: {e}")
            raise
        except Exception as e:
            logger.error(f"Error fetching Binance klines: {e}")
            raise
    
    def get_symbol_info(self, symbol: str) -> Dict:
        """Get symbol information"""
        try:
            url = f"{self.futures_base_url}/fapi/v1/exchangeInfo"
            response = self.session.get(url)
            response.raise_for_status()
            
            data = response.json()
            
            for symbol_info in data['symbols']:
                if symbol_info['symbol'] == symbol.upper():
                    return {
                        'symbol': symbol_info['symbol'],
                        'status': symbol_info['status'],
                        'baseAsset': symbol_info['baseAsset'],
                        'quoteAsset': symbol_info['quoteAsset'],
                        'pricePrecision': symbol_info['pricePrecision'],
                        'quantityPrecision': symbol_info['quantityPrecision']
                    }
            
            raise ValueError(f"Symbol {symbol} not found")
            
        except Exception as e:
            logger.error(f"Error getting symbol info: {e}")
            raise
    
    def get_24hr_ticker(self, symbol: str) -> Dict:
        """Get 24hr ticker statistics"""
        try:
            url = f"{self.futures_base_url}/fapi/v1/ticker/24hr"
            params = {'symbol': symbol.upper()}
            
            response = self.session.get(url, params=params)
            response.raise_for_status()
            
            data = response.json()
            
            return {
                'symbol': data['symbol'],
                'priceChange': float(data['priceChange']),
                'priceChangePercent': float(data['priceChangePercent']),
                'lastPrice': float(data['lastPrice']),
                'volume': float(data['volume']),
                'high': float(data['highPrice']),
                'low': float(data['lowPrice'])
            }
            
        except Exception as e:
            logger.error(f"Error getting 24hr ticker: {e}")
            raise
    
    def validate_symbol(self, symbol: str) -> bool:
        """Validate if symbol exists on Binance"""
        try:
            self.get_symbol_info(symbol)
            return True
        except:
            return False
    
    def get_historical_data(self, symbol: str, timeframe: str, 
                          days_back: int = 30) -> List[Dict]:
        """
        Get historical data for specified number of days
        
        Args:
            symbol: Trading symbol
            timeframe: Timeframe
            days_back: Number of days to go back
            
        Returns:
            List of OHLCV dictionaries
        """
        try:
            end_time = datetime.now()
            start_time = end_time - timedelta(days=days_back)
            
            all_data = []
            current_start = start_time
            
            # Fetch data in chunks (Binance limit is 1500 candles)
            while current_start < end_time:
                # Calculate chunk end time
                chunk_end = min(current_start + timedelta(days=7), end_time)
                
                # Fetch chunk
                chunk_data = self.get_klines(
                    symbol=symbol,
                    timeframe=timeframe,
                    limit=1500,
                    start_time=current_start,
                    end_time=chunk_end
                )
                
                all_data.extend(chunk_data)
                current_start = chunk_end
                
                # Rate limiting
                time.sleep(0.1)
            
            # Remove duplicates and sort
            df = pd.DataFrame(all_data)
            df = df.drop_duplicates(subset=['timestamp']).sort_values('timestamp')
            
            return df.to_dict('records')
            
        except Exception as e:
            logger.error(f"Error getting historical data: {e}")
            raise
    
    def get_server_time(self) -> datetime:
        """Get Binance server time"""
        try:
            url = f"{self.futures_base_url}/fapi/v1/time"
            response = self.session.get(url)
            response.raise_for_status()
            
            data = response.json()
            return datetime.fromtimestamp(data['serverTime'] / 1000)
            
        except Exception as e:
            logger.error(f"Error getting server time: {e}")
            raise
    
    def test_connection(self) -> bool:
        """Test connection to Binance API"""
        try:
            # Add timeout to prevent hanging
            response = self.session.get(
                f"{self.futures_base_url}/fapi/v1/time",
                timeout=5  # 5 second timeout
            )
            if response.status_code == 200:
                logger.info("Binance API connection successful")
                return True
            else:
                logger.error(f"Binance API connection failed: {response.status_code}")
                return False
        except Exception as e:
            logger.error(f"Binance API connection failed: {e}")
            return False


# Global instance
binance_service = BinanceService()
