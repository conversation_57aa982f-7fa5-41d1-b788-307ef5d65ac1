@echo off
echo Strategy Builder - Windows Setup
echo ================================
echo.

echo Step 1: Installing Python packages...
python install_windows.py
if %errorlevel% neq 0 (
    echo ERROR: Package installation failed
    pause
    exit /b 1
)

echo.
echo Step 2: Checking requirements...
python check_requirements.py
if %errorlevel% neq 0 (
    echo ERROR: Requirements check failed
    pause
    exit /b 1
)

echo.
echo Step 3: Creating database...
python create_database.py
if %errorlevel% neq 0 (
    echo ERROR: Database creation failed
    pause
    exit /b 1
)

echo.
echo Step 4: Running tests...
python test_windows.py
if %errorlevel% neq 0 (
    echo ERROR: Tests failed
    pause
    exit /b 1
)

echo.
echo ================================
echo SUCCESS: Strategy Builder is ready!
echo ================================
echo.
echo To start the application:
echo   python run.py
echo.
echo Then open: http://localhost:8000
echo.
pause
