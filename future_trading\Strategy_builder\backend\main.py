"""
FastAPI Backend for Strategy Builder
"""
from fastapi import FastAP<PERSON>, HTTPException, Query
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse, FileResponse
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
import logging
import os
import sys

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.settings import settings, get_cors_origins
from config.database import test_connection, health_check
from backend.api import ohlcv, indicators, marks, strategy
from backend.services.mexc_service import data_service

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Create FastAPI app
app = FastAPI(
    title=settings.app.app_name,
    version=settings.app.version,
    description="Strategy Builder API for manual trading strategy development",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=get_cors_origins(),
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include API routers
app.include_router(ohlcv.router, prefix="/api/ohlcv", tags=["OHLCV Data"])
app.include_router(indicators.router, prefix="/api/indicators", tags=["Technical Indicators"])
app.include_router(marks.router, prefix="/api/marks", tags=["Manual Marks"])
app.include_router(strategy.router, prefix="/api/strategy", tags=["Strategy"])

# Mount static files
static_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), "frontend", "static")
if os.path.exists(static_path):
    app.mount("/static", StaticFiles(directory=static_path), name="static")

# Serve frontend
frontend_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), "frontend")


@app.get("/", response_class=HTMLResponse)
async def serve_frontend():
    """Serve the main frontend page"""
    index_path = os.path.join(frontend_path, "index.html")
    if os.path.exists(index_path):
        return FileResponse(index_path)
    else:
        return HTMLResponse("""
        <html>
            <head><title>Strategy Builder</title></head>
            <body>
                <h1>Strategy Builder API</h1>
                <p>Frontend not found. Please check the frontend directory.</p>
                <p><a href="/docs">API Documentation</a></p>
            </body>
        </html>
        """)


@app.get("/health")
async def health_endpoint():
    """Health check endpoint"""
    try:
        # Test database connection
        db_health = health_check()
        
        # Test exchange connections
        exchange_health = data_service.test_connections()
        
        return {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "version": settings.app.version,
            "database": db_health,
            "exchanges": exchange_health
        }
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(status_code=500, detail="Health check failed")


@app.get("/api/config")
async def get_config():
    """Get application configuration"""
    return {
        "supported_timeframes": settings.app.supported_timeframes,
        "supported_indicators": settings.app.supported_indicators,
        "default_indicator_params": settings.app.default_indicator_params,
        "default_symbol": settings.app.default_symbol,
        "default_timeframe": settings.app.default_timeframe,
        "max_candles": settings.app.max_candles
    }


@app.get("/api/exchanges")
async def get_exchanges():
    """Get supported exchanges"""
    return {
        "exchanges": ["binance", "mexc"],
        "default": "binance"
    }


@app.get("/api/symbols/{exchange}")
async def get_symbols(exchange: str):
    """Get available symbols for an exchange"""
    # This would typically fetch from the exchange API
    # For now, return common symbols
    common_symbols = [
        "BTCUSDT", "ETHUSDT", "BNBUSDT", "ADAUSDT", "DOTUSDT",
        "LINKUSDT", "LTCUSDT", "BCHUSDT", "XLMUSDT", "EOSUSDT"
    ]
    
    return {
        "exchange": exchange,
        "symbols": common_symbols
    }


@app.on_event("startup")
async def startup_event():
    """Startup event handler"""
    logger.info(f"Starting {settings.app.app_name} v{settings.app.version}")

    # Test database connection
    if not test_connection():
        logger.error("Database connection failed!")
        # Don't exit, but log the error
    else:
        logger.info("Database connection successful")

    # Skip exchange API tests during startup to prevent hanging
    logger.info("Exchange API connections will be tested on first use")


@app.on_event("shutdown")
async def shutdown_event():
    """Shutdown event handler"""
    logger.info("Shutting down Strategy Builder API")


# Error handlers
@app.exception_handler(ValueError)
async def value_error_handler(request, exc):
    return HTTPException(status_code=400, detail=str(exc))


@app.exception_handler(Exception)
async def general_exception_handler(request, exc):
    logger.error(f"Unhandled exception: {exc}")
    return HTTPException(status_code=500, detail="Internal server error")


if __name__ == "__main__":
    import uvicorn
    
    # Run the application
    uvicorn.run(
        "main:app",
        host=settings.app.host,
        port=settings.app.port,
        reload=settings.app.debug,
        log_level="info"
    )
