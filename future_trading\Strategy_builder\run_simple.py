#!/usr/bin/env python3
"""
Simple server runner for Strategy Builder
Bypasses complex startup checks and runs the server directly
"""
import sys
import os
import logging
import uvicorn

# Add current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def main():
    """Main function to run the server"""
    try:
        logger.info("🚀 Starting Strategy Builder server (simple mode)...")
        logger.info("📍 Server will be available at: http://127.0.0.1:8000")
        logger.info("📖 API documentation: http://127.0.0.1:8000/docs")
        
        # Run the server directly
        uvicorn.run(
            "backend.main:app",
            host="127.0.0.1",
            port=8000,
            reload=False,
            log_level="info"
        )
        
    except KeyboardInterrupt:
        logger.info("🛑 Server stopped by user")
    except Exception as e:
        logger.error(f"❌ Server error: {e}")
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
