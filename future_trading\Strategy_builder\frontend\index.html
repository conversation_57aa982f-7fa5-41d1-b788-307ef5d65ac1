<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Strategy Builder - Phase 1 MVP</title>
    <link rel="stylesheet" href="static/css/styles.css">
    <script src="https://unpkg.com/lightweight-charts/dist/lightweight-charts.standalone.production.js"></script>
</head>
<body>
    <div class="app-container">
        <!-- Header -->
        <header class="app-header">
            <h1>Strategy Builder</h1>
            <div class="header-controls">
                <div class="session-info">
                    <span id="session-name">No Session</span>
                    <button id="new-session-btn" class="btn btn-primary">New Session</button>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Left Sidebar - Controls -->
            <aside class="sidebar">
                <div class="control-panel">
                    <!-- Symbol & Exchange Selection -->
                    <div class="control-group">
                        <h3>Market Data</h3>
                        <div class="form-group">
                            <label for="exchange-select">Exchange:</label>
                            <select id="exchange-select">
                                <option value="binance">Binance</option>
                                <option value="mexc">MEXC</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="symbol-input">Symbol:</label>
                            <input type="text" id="symbol-input" value="BTCUSDT" placeholder="BTCUSDT">
                        </div>
                        <div class="form-group">
                            <label for="timeframe-select">Timeframe:</label>
                            <select id="timeframe-select">
                                <option value="1m">1m</option>
                                <option value="3m">3m</option>
                                <option value="5m">5m</option>
                                <option value="15m">15m</option>
                                <option value="30m">30m</option>
                                <option value="1h" selected>1h</option>
                                <option value="2h">2h</option>
                                <option value="4h">4h</option>
                                <option value="1d">1d</option>
                                <option value="1w">1w</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="candles-input">Candles:</label>
                            <input type="number" id="candles-input" value="500" min="50" max="5000">
                        </div>
                        <button id="load-data-btn" class="btn btn-primary">Load Data</button>
                    </div>

                    <!-- Indicators -->
                    <div class="control-group">
                        <h3>Technical Indicators</h3>
                        <div class="indicators-list">
                            <div class="indicator-item">
                                <input type="checkbox" id="rsi-checkbox" checked>
                                <label for="rsi-checkbox">RSI (14)</label>
                                <button class="config-btn" data-indicator="RSI">⚙️</button>
                            </div>
                            <div class="indicator-item">
                                <input type="checkbox" id="macd-checkbox" checked>
                                <label for="macd-checkbox">MACD (12,26,9)</label>
                                <button class="config-btn" data-indicator="MACD">⚙️</button>
                            </div>
                            <div class="indicator-item">
                                <input type="checkbox" id="ema-checkbox">
                                <label for="ema-checkbox">EMA (20)</label>
                                <button class="config-btn" data-indicator="EMA">⚙️</button>
                            </div>
                        </div>
                        <button id="calculate-indicators-btn" class="btn btn-secondary">Calculate Indicators</button>
                    </div>

                    <!-- Trade Marking -->
                    <div class="control-group">
                        <h3>Trade Marking</h3>
                        <div class="marking-controls">
                            <button id="mark-entry-buy-btn" class="btn btn-success">Mark Entry (Buy)</button>
                            <button id="mark-entry-sell-btn" class="btn btn-danger">Mark Entry (Sell)</button>
                            <button id="mark-exit-btn" class="btn btn-warning" disabled>Mark Exit</button>
                        </div>
                        <div class="open-trades">
                            <h4>Open Trades</h4>
                            <div id="open-trades-list"></div>
                        </div>
                    </div>

                    <!-- Export -->
                    <div class="control-group">
                        <h3>Export</h3>
                        <button id="export-json-btn" class="btn btn-secondary">Export JSON</button>
                        <button id="export-csv-btn" class="btn btn-secondary">Export CSV</button>
                    </div>
                </div>
            </aside>

            <!-- Chart Area -->
            <main class="chart-container">
                <div class="chart-header">
                    <div class="chart-title">
                        <span id="chart-symbol">BTCUSDT</span>
                        <span id="chart-timeframe">1h</span>
                        <span id="chart-status">Ready</span>
                    </div>
                    <div class="chart-controls">
                        <button id="zoom-in-btn">🔍+</button>
                        <button id="zoom-out-btn">🔍-</button>
                        <button id="reset-zoom-btn">↻</button>
                    </div>
                </div>
                <div id="chart" class="chart-area"></div>
                
                <!-- Indicator Panels -->
                <div id="indicator-panels" class="indicator-panels">
                    <div id="rsi-panel" class="indicator-panel" style="display: none;">
                        <div class="panel-header">RSI</div>
                        <div id="rsi-chart" class="indicator-chart"></div>
                    </div>
                    <div id="macd-panel" class="indicator-panel" style="display: none;">
                        <div class="panel-header">MACD</div>
                        <div id="macd-chart" class="indicator-chart"></div>
                    </div>
                </div>
            </main>

            <!-- Right Sidebar - Trade Log -->
            <aside class="trade-sidebar">
                <div class="trade-log">
                    <h3>Trade Log</h3>
                    <div class="trade-stats">
                        <div class="stat-item">
                            <span class="stat-label">Total Trades:</span>
                            <span id="total-trades">0</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Win Rate:</span>
                            <span id="win-rate">0%</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Total P&L:</span>
                            <span id="total-pnl">0%</span>
                        </div>
                    </div>
                    <div id="trade-list" class="trade-list"></div>
                </div>
            </aside>
        </div>

        <!-- Status Bar -->
        <footer class="status-bar">
            <div class="status-left">
                <span id="connection-status">Disconnected</span>
                <span id="data-status">No Data</span>
            </div>
            <div class="status-right">
                <span id="last-update">Never</span>
            </div>
        </footer>
    </div>

    <!-- Modals -->
    <!-- New Session Modal -->
    <div id="new-session-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Create New Session</h3>
                <span class="close">&times;</span>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label for="session-name-input">Session Name:</label>
                    <input type="text" id="session-name-input" placeholder="Optional session name">
                </div>
                <div class="form-group">
                    <label for="session-symbol-input">Symbol:</label>
                    <input type="text" id="session-symbol-input" value="BTCUSDT">
                </div>
                <div class="form-group">
                    <label for="session-timeframe-select">Timeframe:</label>
                    <select id="session-timeframe-select">
                        <option value="1h">1h</option>
                        <option value="4h">4h</option>
                        <option value="1d">1d</option>
                    </select>
                </div>
            </div>
            <div class="modal-footer">
                <button id="create-session-btn" class="btn btn-primary">Create Session</button>
                <button class="btn btn-secondary modal-close">Cancel</button>
            </div>
        </div>
    </div>

    <!-- Indicator Config Modal -->
    <div id="indicator-config-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="indicator-config-title">Configure Indicator</h3>
                <span class="close">&times;</span>
            </div>
            <div class="modal-body" id="indicator-config-body">
                <!-- Dynamic content based on indicator -->
            </div>
            <div class="modal-footer">
                <button id="save-indicator-config-btn" class="btn btn-primary">Save</button>
                <button class="btn btn-secondary modal-close">Cancel</button>
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div id="loading-overlay" class="loading-overlay" style="display: none;">
        <div class="loading-spinner"></div>
        <div class="loading-text">Loading...</div>
    </div>

    <!-- Scripts -->
    <script src="static/js/config.js"></script>
    <script src="static/js/api.js"></script>
    <script src="static/js/chart.js"></script>
    <script src="static/js/indicators.js"></script>
    <script src="static/js/trading.js"></script>
    <script src="static/js/app.js"></script>
</body>
</html>
