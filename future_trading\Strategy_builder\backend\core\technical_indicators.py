"""
Technical Indicators Module for Strategy Builder
Implements RSI, MACD, EMA, SMA, Bollinger Bands, and Stochastic indicators
"""
import pandas as pd
import numpy as np
import pandas_ta as ta
from typing import Dict, List, Any, Optional
import logging

logger = logging.getLogger(__name__)


class TechnicalIndicators:
    """Technical indicators calculator"""
    
    def __init__(self):
        self.supported_indicators = {
            'RSI': self.calculate_rsi,
            'MACD': self.calculate_macd,
            'EMA': self.calculate_ema,
            'SMA': self.calculate_sma,
            'BB': self.calculate_bollinger_bands,
            'STOCH': self.calculate_stochastic
        }
    
    def calculate_indicators(self, ohlcv_data: List[Dict], 
                           indicators_config: List[Dict]) -> Dict[str, List[Dict]]:
        """
        Calculate multiple indicators for OHLCV data
        
        Args:
            ohlcv_data: List of OHLCV dictionaries
            indicators_config: List of indicator configurations
            
        Returns:
            Dictionary with indicator names as keys and calculated values as lists
        """
        try:
            # Convert to DataFrame
            df = pd.DataFrame(ohlcv_data)
            df['timestamp'] = pd.to_datetime(df['timestamp'])
            df = df.sort_values('timestamp')
            
            results = {}
            
            for config in indicators_config:
                indicator_name = config['name'].upper()
                params = config.get('params', {})
                
                if indicator_name in self.supported_indicators:
                    indicator_data = self.supported_indicators[indicator_name](df, params)
                    results[indicator_name] = indicator_data
                else:
                    logger.warning(f"Unsupported indicator: {indicator_name}")
            
            return results
            
        except Exception as e:
            logger.error(f"Error calculating indicators: {e}")
            raise
    
    def calculate_rsi(self, df: pd.DataFrame, params: Dict) -> List[Dict]:
        """Calculate RSI (Relative Strength Index)"""
        try:
            length = params.get('length', 14)
            
            rsi_values = ta.rsi(df['close'], length=length)
            
            result = []
            for i, (timestamp, rsi) in enumerate(zip(df['timestamp'], rsi_values)):
                result.append({
                    'timestamp': timestamp.isoformat(),
                    'value': {
                        'rsi': float(rsi) if not pd.isna(rsi) else None
                    }
                })
            
            return result
            
        except Exception as e:
            logger.error(f"Error calculating RSI: {e}")
            raise
    
    def calculate_macd(self, df: pd.DataFrame, params: Dict) -> List[Dict]:
        """Calculate MACD (Moving Average Convergence Divergence)"""
        try:
            fast = params.get('fast', 12)
            slow = params.get('slow', 26)
            signal = params.get('signal', 9)
            
            macd_data = ta.macd(df['close'], fast=fast, slow=slow, signal=signal)
            
            result = []
            for i, timestamp in enumerate(df['timestamp']):
                macd_value = macd_data.iloc[i, 0] if len(macd_data.columns) > 0 else None
                histogram_value = macd_data.iloc[i, 1] if len(macd_data.columns) > 1 else None
                signal_value = macd_data.iloc[i, 2] if len(macd_data.columns) > 2 else None
                
                result.append({
                    'timestamp': timestamp.isoformat(),
                    'value': {
                        'macd': float(macd_value) if not pd.isna(macd_value) else None,
                        'histogram': float(histogram_value) if not pd.isna(histogram_value) else None,
                        'signal': float(signal_value) if not pd.isna(signal_value) else None
                    }
                })
            
            return result
            
        except Exception as e:
            logger.error(f"Error calculating MACD: {e}")
            raise
    
    def calculate_ema(self, df: pd.DataFrame, params: Dict) -> List[Dict]:
        """Calculate EMA (Exponential Moving Average)"""
        try:
            length = params.get('length', 20)
            
            ema_values = ta.ema(df['close'], length=length)
            
            result = []
            for i, (timestamp, ema) in enumerate(zip(df['timestamp'], ema_values)):
                result.append({
                    'timestamp': timestamp.isoformat(),
                    'value': {
                        'ema': float(ema) if not pd.isna(ema) else None
                    }
                })
            
            return result
            
        except Exception as e:
            logger.error(f"Error calculating EMA: {e}")
            raise
    
    def calculate_sma(self, df: pd.DataFrame, params: Dict) -> List[Dict]:
        """Calculate SMA (Simple Moving Average)"""
        try:
            length = params.get('length', 20)
            
            sma_values = ta.sma(df['close'], length=length)
            
            result = []
            for i, (timestamp, sma) in enumerate(zip(df['timestamp'], sma_values)):
                result.append({
                    'timestamp': timestamp.isoformat(),
                    'value': {
                        'sma': float(sma) if not pd.isna(sma) else None
                    }
                })
            
            return result
            
        except Exception as e:
            logger.error(f"Error calculating SMA: {e}")
            raise
    
    def calculate_bollinger_bands(self, df: pd.DataFrame, params: Dict) -> List[Dict]:
        """Calculate Bollinger Bands"""
        try:
            length = params.get('length', 20)
            std = params.get('std', 2)
            
            bb_data = ta.bbands(df['close'], length=length, std=std)
            
            result = []
            for i, timestamp in enumerate(df['timestamp']):
                lower = bb_data.iloc[i, 0] if len(bb_data.columns) > 0 else None
                mid = bb_data.iloc[i, 1] if len(bb_data.columns) > 1 else None
                upper = bb_data.iloc[i, 2] if len(bb_data.columns) > 2 else None
                
                result.append({
                    'timestamp': timestamp.isoformat(),
                    'value': {
                        'bb_lower': float(lower) if not pd.isna(lower) else None,
                        'bb_middle': float(mid) if not pd.isna(mid) else None,
                        'bb_upper': float(upper) if not pd.isna(upper) else None
                    }
                })
            
            return result
            
        except Exception as e:
            logger.error(f"Error calculating Bollinger Bands: {e}")
            raise
    
    def calculate_stochastic(self, df: pd.DataFrame, params: Dict) -> List[Dict]:
        """Calculate Stochastic Oscillator"""
        try:
            k = params.get('k', 14)
            d = params.get('d', 3)
            smooth_k = params.get('smooth_k', 3)
            
            stoch_data = ta.stoch(df['high'], df['low'], df['close'], 
                                k=k, d=d, smooth_k=smooth_k)
            
            result = []
            for i, timestamp in enumerate(df['timestamp']):
                stoch_k = stoch_data.iloc[i, 0] if len(stoch_data.columns) > 0 else None
                stoch_d = stoch_data.iloc[i, 1] if len(stoch_data.columns) > 1 else None
                
                result.append({
                    'timestamp': timestamp.isoformat(),
                    'value': {
                        'stoch_k': float(stoch_k) if not pd.isna(stoch_k) else None,
                        'stoch_d': float(stoch_d) if not pd.isna(stoch_d) else None
                    }
                })
            
            return result
            
        except Exception as e:
            logger.error(f"Error calculating Stochastic: {e}")
            raise
    
    def get_indicator_snapshot(self, ohlcv_data: List[Dict], 
                             indicators_config: List[Dict],
                             timestamp: str) -> Dict[str, Any]:
        """
        Get indicator values snapshot for a specific timestamp
        
        Args:
            ohlcv_data: OHLCV data
            indicators_config: Indicator configurations
            timestamp: Target timestamp
            
        Returns:
            Dictionary with indicator values at the specified timestamp
        """
        try:
            indicators_data = self.calculate_indicators(ohlcv_data, indicators_config)
            snapshot = {}
            
            for indicator_name, data in indicators_data.items():
                # Find the data point for the specified timestamp
                for point in data:
                    if point['timestamp'] == timestamp:
                        snapshot[indicator_name] = point['value']
                        break
            
            return snapshot
            
        except Exception as e:
            logger.error(f"Error getting indicator snapshot: {e}")
            raise
    
    def validate_indicator_config(self, config: Dict) -> bool:
        """Validate indicator configuration"""
        try:
            required_fields = ['name']
            for field in required_fields:
                if field not in config:
                    return False
            
            indicator_name = config['name'].upper()
            if indicator_name not in self.supported_indicators:
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error validating indicator config: {e}")
            return False
    
    def get_supported_indicators(self) -> List[str]:
        """Get list of supported indicators"""
        return list(self.supported_indicators.keys())
    
    def get_default_params(self, indicator_name: str) -> Dict:
        """Get default parameters for an indicator"""
        defaults = {
            'RSI': {'length': 14},
            'MACD': {'fast': 12, 'slow': 26, 'signal': 9},
            'EMA': {'length': 20},
            'SMA': {'length': 20},
            'BB': {'length': 20, 'std': 2},
            'STOCH': {'k': 14, 'd': 3, 'smooth_k': 3}
        }
        
        return defaults.get(indicator_name.upper(), {})


# Global instance
technical_indicators = TechnicalIndicators()


def calculate_profit_pnl(entry_price: float, exit_price: float, 
                        entry_side: str, leverage: float = 1.0) -> Dict[str, float]:
    """
    Calculate profit/loss for futures trading
    
    Args:
        entry_price: Entry price
        exit_price: Exit price
        entry_side: 'buy' or 'sell'
        leverage: Leverage multiplier
        
    Returns:
        Dictionary with profit percentage and absolute values
    """
    try:
        if entry_side.lower() == 'buy':
            # Long position
            profit_pct = ((exit_price - entry_price) / entry_price) * 100
        else:
            # Short position
            profit_pct = ((entry_price - exit_price) / entry_price) * 100
        
        # Apply leverage
        profit_pct_leveraged = profit_pct * leverage
        
        # Calculate absolute profit (assuming $1000 position size)
        position_size = 1000
        profit_absolute = (profit_pct / 100) * position_size * leverage
        
        return {
            'profit_pct': round(profit_pct, 4),
            'profit_pct_leveraged': round(profit_pct_leveraged, 4),
            'profit_absolute': round(profit_absolute, 2)
        }
        
    except Exception as e:
        logger.error(f"Error calculating PnL: {e}")
        raise
