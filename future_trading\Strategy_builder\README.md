# Strategy Builder App - Phase 1 MVP

A comprehensive manual trading strategy development tool with interactive charts, technical indicators, and trade marking capabilities.

## 🚀 Quick Start

### Prerequisites

- Python 3.8+
- MySQL 5.7+ or 8.0+
- Modern web browser

### Installation & Setup

1. **Clone and navigate to the project**

   ```bash
   cd future_trading/Strategy_builder
   ```

2. **Install dependencies**

   ```bash
   python run.py --install-deps
   ```

3. **Configure environment**

   ```bash
   cp .env.example .env
   # Edit .env with your database and API credentials
   ```

4. **Setup database**

   ```bash
   python run.py --setup-db
   ```

5. **Run the application**

   ```bash
   python run.py
   ```

6. **Access the application**
   - Web Interface: http://localhost:8000
   - API Documentation: http://localhost:8000/docs

## 📁 Project Structure

```
Strategy_builder/
├── backend/                    # FastAPI backend
│   ├── api/                   # API endpoints
│   │   ├── ohlcv.py          # OHLCV data endpoints
│   │   ├── indicators.py     # Technical indicators endpoints
│   │   ├── marks.py          # Trade marking endpoints
│   │   └── strategy.py       # Strategy management endpoints
│   ├── core/                  # Core business logic
│   │   └── technical_indicators.py  # Indicator calculations
│   ├── database/              # Database models and operations
│   │   ├── models.py         # SQLAlchemy models
│   │   └── operations.py     # Database operations
│   ├── services/              # External services
│   │   ├── binance_service.py # Binance API client
│   │   └── mexc_service.py   # MEXC API client
│   └── main.py               # FastAPI application entry point
├── frontend/                  # Web frontend
│   ├── static/               # Static assets
│   │   ├── css/styles.css    # Application styles
│   │   └── js/               # JavaScript modules
│   │       ├── config.js     # Configuration and constants
│   │       ├── api.js        # API client
│   │       ├── chart.js      # Chart management
│   │       ├── indicators.js # Indicator management
│   │       ├── trading.js    # Trade marking
│   │       └── app.js        # Main application
│   └── index.html           # Main application page
├── database/                  # Database scripts
│   ├── schema.sql           # Database schema
│   └── setup_database.py   # Database setup script
├── config/                   # Configuration files
│   ├── settings.py          # Application settings
│   └── database.py          # Database configuration
├── requirements.txt          # Python dependencies
├── run.py                   # Startup script
├── .env.example             # Environment configuration template
└── README.md               # This file
```

## ✨ Features (Phase 1 MVP)

### Core Features

- ✅ **Interactive Candlestick Chart**: TradingView lightweight-charts integration
- ✅ **Multiple Timeframes**: 1m, 3m, 5m, 15m, 30m, 1h, 2h, 4h, 1D, 1W
- ✅ **Exchange Support**: Binance and MEXC APIs
- ✅ **Data Management**: Configurable data range and caching

### Technical Analysis

- ✅ **Technical Indicators**: RSI, MACD, EMA, SMA, Bollinger Bands, Stochastic
- ✅ **Configurable Parameters**: Customizable indicator settings
- ✅ **Multiple Display Modes**: Overlays and sub-panels
- ✅ **Real-time Calculation**: On-demand indicator computation

### Trade Management

- ✅ **Manual Trade Marking**: Right-click entry/exit marking
- ✅ **Trade Types**: Buy/Sell entries with linked exits
- ✅ **Indicator Snapshots**: Automatic capture of indicator values
- ✅ **Profit Calculation**: Futures-style PnL calculation
- ✅ **Session Management**: Organized trade sessions

### Data & Analytics

- ✅ **Trade Statistics**: Win rate, total P&L, trade count
- ✅ **Export Capabilities**: JSON and CSV export
- ✅ **Session Analytics**: Detailed performance metrics
- ✅ **Database Storage**: Persistent data storage

## 🛠️ Tech Stack

- **Backend**: Python FastAPI, SQLAlchemy, pandas-ta
- **Frontend**: HTML5, CSS3, Vanilla JavaScript
- **Charting**: TradingView lightweight-charts
- **Database**: MySQL with connection pooling
- **APIs**: Binance Futures, MEXC REST APIs
- **Indicators**: pandas-ta, custom implementations

## 📊 Database Schema

### Core Tables

- **`ohlcv_data`**: OHLCV candlestick data with indexing
- **`indicators_data`**: Technical indicator values and parameters
- **`manual_marks`**: User-marked entry/exit points
- **`strategy_log`**: Complete trade records with P&L
- **`strategy_sessions`**: Session management and statistics

### Key Features

- Optimized indexes for fast queries
- JSON storage for flexible indicator data
- Foreign key relationships for data integrity
- Automatic timestamp tracking

## 🔧 Configuration

### Environment Variables

Create a `.env` file from `.env.example` and configure:

```bash
# Database Configuration
DB_HOST=localhost
DB_PORT=3306
DB_USERNAME=root
DB_PASSWORD=your_password
DB_DATABASE=strategy_builder

# API Keys (Optional for data fetching)
BINANCE_API_KEY=your_binance_key
BINANCE_API_SECRET=your_binance_secret
MEXC_API_KEY=your_mexc_key
MEXC_API_SECRET=your_mexc_secret
```

### Application Settings

Key configuration options in `config/settings.py`:

- **Default Symbol**: BTCUSDT
- **Default Timeframe**: 1h
- **Max Candles**: 5000
- **Supported Indicators**: RSI, MACD, EMA, SMA, BB, STOCH
- **Chart Height**: 400px
- **Indicator Panel Height**: 150px

## 📖 Usage Guide

### 1. Creating a Session

1. Click "New Session" button
2. Enter session name (optional)
3. Select symbol and timeframe
4. Click "Create Session"

### 2. Loading Chart Data

1. Select exchange (Binance/MEXC)
2. Enter trading symbol (e.g., BTCUSDT)
3. Choose timeframe
4. Set number of candles
5. Click "Load Data"

### 3. Adding Technical Indicators

1. Check desired indicators (RSI, MACD, EMA)
2. Configure parameters using ⚙️ button
3. Click "Calculate Indicators"
4. Indicators appear as overlays or sub-panels

### 4. Marking Trades

1. Click on chart at desired entry point
2. Click "Mark Entry (Buy)" or "Mark Entry (Sell)"
3. Click on chart at exit point
4. Click "Mark Exit"
5. Trade appears in trade log with P&L

### 5. Exporting Data

1. Click "Export JSON" for complete session data
2. Click "Export CSV" for trade summary
3. Files download automatically

## 🎯 API Endpoints

### OHLCV Data

- `GET /api/ohlcv/{exchange}/{symbol}/{timeframe}` - Fetch OHLCV data
- `GET /api/ohlcv/{exchange}/{symbol}/{timeframe}/latest` - Get latest candle

### Technical Indicators

- `POST /api/indicators/calculate` - Calculate indicators
- `GET /api/indicators/supported` - List supported indicators
- `POST /api/indicators/snapshot` - Get indicator snapshot

### Trade Marking

- `POST /api/marks/entry` - Create entry mark
- `POST /api/marks/exit` - Create exit mark
- `GET /api/marks/session/{session_id}` - Get session marks

### Strategy Management

- `POST /api/strategy/session/create` - Create new session
- `GET /api/strategy/session/{session_id}` - Get session details
- `GET /api/strategy/analytics/{session_id}` - Get session analytics
- `GET /api/strategy/export/{session_id}/json` - Export JSON
- `GET /api/strategy/export/{session_id}/csv` - Export CSV

## ⌨️ Keyboard Shortcuts

- **Ctrl+R**: Refresh chart data
- **Ctrl+N**: Create new session
- **Ctrl+E**: Export session as JSON
- **B**: Mark buy entry (when chart focused)
- **S**: Mark sell entry (when chart focused)
- **X**: Mark exit (when chart focused)

## 🚨 Troubleshooting

### Database Connection Issues

1. **Check MySQL service**: Ensure MySQL is running
2. **Verify credentials**: Check `.env` file settings
3. **Test connection**: Run `python run.py --check-only`
4. **Reset database**: Run `python run.py --setup-db`

### API Connection Issues

1. **Check internet connection**
2. **Verify API endpoints** in browser
3. **Check CORS settings** in browser console
4. **Restart server**: `python run.py`

### Chart Not Loading

1. **Check browser console** for JavaScript errors
2. **Verify data response** in Network tab
3. **Clear browser cache**
4. **Try different symbol/timeframe**

### Performance Issues

1. **Reduce candle count** (< 1000 for better performance)
2. **Limit active indicators** (< 5 simultaneously)
3. **Clear old session data**
4. **Check database indexes**

## 🔮 Future Enhancements (Phase 2+)

### Phase 2 - Advanced Features

- [ ] Strategy rule generator from marked trades
- [ ] Pattern recognition and suggestions
- [ ] Advanced analytics and metrics
- [ ] Multiple session comparison

### Phase 3 - Automation

- [ ] Backtesting engine
- [ ] Strategy validation
- [ ] Performance optimization
- [ ] Risk management tools

### Phase 4 - Collaboration

- [ ] Strategy sharing
- [ ] Cloud synchronization
- [ ] Team collaboration features
- [ ] Strategy marketplace

## 📝 License

This project is part of the Autotradememe trading system. All rights reserved.

## 🤝 Contributing

1. Follow the existing code structure
2. Add tests for new features
3. Update documentation
4. Submit pull requests for review

## 📞 Support

For issues and questions:

1. Check the troubleshooting section
2. Review API documentation at `/docs`
3. Check database logs
4. Contact development team
