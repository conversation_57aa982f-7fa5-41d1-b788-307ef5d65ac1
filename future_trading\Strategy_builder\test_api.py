#!/usr/bin/env python3
"""
Comprehensive API Test Script for Strategy Builder
Tests all available API endpoints and validates responses
"""
import requests
import json
import time
from datetime import datetime
from typing import Dict, Any, List
import sys

class StrategyBuilderAPITester:
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        self.test_results = []
        
    def log_test(self, test_name: str, success: bool, details: str = ""):
        """Log test result"""
        status = "✅ PASS" if success else "❌ FAIL"
        result = {
            "test": test_name,
            "status": status,
            "success": success,
            "details": details,
            "timestamp": datetime.now().isoformat()
        }
        self.test_results.append(result)
        print(f"{status} {test_name}")
        if details:
            print(f"    {details}")
        
    def test_health_endpoints(self):
        """Test health check endpoints"""
        print("\n🔍 Testing Health Endpoints...")
        
        # Test /health endpoint
        try:
            response = self.session.get(f"{self.base_url}/health", timeout=5)
            if response.status_code == 200:
                data = response.json()
                self.log_test("GET /health", True, f"Status: {data.get('status')}")
            else:
                self.log_test("GET /health", False, f"Status code: {response.status_code}")
        except Exception as e:
            self.log_test("GET /health", False, f"Error: {str(e)}")
            
        # Test /api/health endpoint
        try:
            response = self.session.get(f"{self.base_url}/api/health", timeout=5)
            if response.status_code == 200:
                data = response.json()
                self.log_test("GET /api/health", True, f"Service: {data.get('service')}, DB: {data.get('database')}")
            else:
                self.log_test("GET /api/health", False, f"Status code: {response.status_code}")
        except Exception as e:
            self.log_test("GET /api/health", False, f"Error: {str(e)}")
    
    def test_config_endpoints(self):
        """Test configuration endpoints"""
        print("\n🔍 Testing Configuration Endpoints...")
        
        # Test /api/config endpoint
        try:
            response = self.session.get(f"{self.base_url}/api/config", timeout=5)
            if response.status_code == 200:
                data = response.json()
                bb_config = data.get('default_indicator_params', {}).get('BB', {})
                bb_length = bb_config.get('length', 'N/A')
                self.log_test("GET /api/config", True, f"BB length: {bb_length}, Indicators: {len(data.get('supported_indicators', []))}")
            else:
                self.log_test("GET /api/config", False, f"Status code: {response.status_code}")
        except Exception as e:
            self.log_test("GET /api/config", False, f"Error: {str(e)}")
            
        # Test /api/exchanges endpoint
        try:
            response = self.session.get(f"{self.base_url}/api/exchanges", timeout=5)
            if response.status_code == 200:
                data = response.json()
                exchanges = data.get('exchanges', [])
                self.log_test("GET /api/exchanges", True, f"Exchanges: {', '.join(exchanges)}")
            else:
                self.log_test("GET /api/exchanges", False, f"Status code: {response.status_code}")
        except Exception as e:
            self.log_test("GET /api/exchanges", False, f"Error: {str(e)}")
    
    def test_ohlcv_endpoints(self):
        """Test OHLCV data endpoints"""
        print("\n🔍 Testing OHLCV Endpoints...")
        
        # Test OHLCV data retrieval
        test_params = [
            ("binance", "BTCUSDT", "1h", 100),
            ("mexc", "ETHUSDT", "4h", 50),
        ]
        
        for exchange, symbol, timeframe, limit in test_params:
            try:
                url = f"{self.base_url}/api/ohlcv/{exchange}/{symbol}/{timeframe}"
                params = {"limit": limit}
                response = self.session.get(url, params=params, timeout=10)
                
                if response.status_code == 200:
                    data = response.json()
                    if data.get('success'):
                        ohlcv_data = data.get('data', [])
                        self.log_test(f"GET /api/ohlcv/{exchange}/{symbol}/{timeframe}", True, 
                                    f"Retrieved {len(ohlcv_data)} candles")
                    else:
                        self.log_test(f"GET /api/ohlcv/{exchange}/{symbol}/{timeframe}", False, 
                                    "Success=False in response")
                else:
                    self.log_test(f"GET /api/ohlcv/{exchange}/{symbol}/{timeframe}", False, 
                                f"Status code: {response.status_code}")
            except Exception as e:
                self.log_test(f"GET /api/ohlcv/{exchange}/{symbol}/{timeframe}", False, f"Error: {str(e)}")
    
    def test_indicators_endpoints(self):
        """Test technical indicators endpoints"""
        print("\n🔍 Testing Indicators Endpoints...")
        
        # Test supported indicators
        try:
            response = self.session.get(f"{self.base_url}/api/indicators/supported", timeout=5)
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    indicators = data.get('indicators', [])
                    bb_found = any(ind.get('name') == 'BB' for ind in indicators)
                    self.log_test("GET /api/indicators/supported", True, 
                                f"Found {len(indicators)} indicators, BB included: {bb_found}")
                else:
                    self.log_test("GET /api/indicators/supported", False, "Success=False in response")
            else:
                self.log_test("GET /api/indicators/supported", False, f"Status code: {response.status_code}")
        except Exception as e:
            self.log_test("GET /api/indicators/supported", False, f"Error: {str(e)}")
        
        # Test indicator calculation
        try:
            payload = {
                "symbol": "BTCUSDT",
                "timeframe": "1h",
                "indicators": ["BB", "RSI", "SMA", "EMA"],
                "params": {
                    "BB": {"length": 4, "std": 2},
                    "RSI": {"length": 14},
                    "SMA": {"length": 20},
                    "EMA": {"length": 20}
                }
            }
            
            response = self.session.post(f"{self.base_url}/api/indicators/calculate", 
                                       json=payload, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    indicators_data = data.get('data', {})
                    calculated = list(indicators_data.keys())
                    self.log_test("POST /api/indicators/calculate", True, 
                                f"Calculated: {', '.join(calculated)}")
                else:
                    self.log_test("POST /api/indicators/calculate", False, "Success=False in response")
            else:
                self.log_test("POST /api/indicators/calculate", False, f"Status code: {response.status_code}")
        except Exception as e:
            self.log_test("POST /api/indicators/calculate", False, f"Error: {str(e)}")
    
    def test_marks_endpoints(self):
        """Test manual marks endpoints"""
        print("\n🔍 Testing Marks Endpoints...")
        
        # Test entry mark creation
        try:
            payload = {
                "symbol": "BTCUSDT",
                "timestamp": int(time.time() * 1000),
                "price": 50000,
                "type": "buy",
                "notes": "Test entry mark"
            }
            
            response = self.session.post(f"{self.base_url}/api/marks/entry", 
                                       json=payload, timeout=5)
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    mark_id = data.get('id', 'N/A')
                    self.log_test("POST /api/marks/entry", True, f"Created mark ID: {mark_id}")
                else:
                    self.log_test("POST /api/marks/entry", False, "Success=False in response")
            else:
                self.log_test("POST /api/marks/entry", False, f"Status code: {response.status_code}")
        except Exception as e:
            self.log_test("POST /api/marks/entry", False, f"Error: {str(e)}")
        
        # Test exit mark creation
        try:
            payload = {
                "symbol": "BTCUSDT",
                "timestamp": int(time.time() * 1000),
                "price": 51000,
                "notes": "Test exit mark"
            }
            
            response = self.session.post(f"{self.base_url}/api/marks/exit", 
                                       json=payload, timeout=5)
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    mark_id = data.get('id', 'N/A')
                    self.log_test("POST /api/marks/exit", True, f"Created mark ID: {mark_id}")
                else:
                    self.log_test("POST /api/marks/exit", False, "Success=False in response")
            else:
                self.log_test("POST /api/marks/exit", False, f"Status code: {response.status_code}")
        except Exception as e:
            self.log_test("POST /api/marks/exit", False, f"Error: {str(e)}")
    
    def test_strategy_endpoints(self):
        """Test strategy session endpoints"""
        print("\n🔍 Testing Strategy Endpoints...")
        
        # Test strategy session creation
        try:
            payload = {
                "name": "Test Strategy Session",
                "symbol": "BTCUSDT",
                "timeframe": "1h",
                "description": "Automated test session"
            }
            
            response = self.session.post(f"{self.base_url}/api/strategy/session/create", 
                                       json=payload, timeout=5)
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    session_id = data.get('session_id', 'N/A')
                    self.log_test("POST /api/strategy/session/create", True, f"Session ID: {session_id}")
                else:
                    self.log_test("POST /api/strategy/session/create", False, "Success=False in response")
            else:
                self.log_test("POST /api/strategy/session/create", False, f"Status code: {response.status_code}")
        except Exception as e:
            self.log_test("POST /api/strategy/session/create", False, f"Error: {str(e)}")
        
        # Test strategy sessions retrieval
        try:
            response = self.session.get(f"{self.base_url}/api/strategy/sessions", timeout=5)
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    sessions = data.get('sessions', [])
                    self.log_test("GET /api/strategy/sessions", True, f"Found {len(sessions)} sessions")
                else:
                    self.log_test("GET /api/strategy/sessions", False, "Success=False in response")
            else:
                self.log_test("GET /api/strategy/sessions", False, f"Status code: {response.status_code}")
        except Exception as e:
            self.log_test("GET /api/strategy/sessions", False, f"Error: {str(e)}")
    
    def test_symbols_endpoint(self):
        """Test symbols endpoint"""
        print("\n🔍 Testing Symbols Endpoints...")
        
        for exchange in ["binance", "mexc"]:
            try:
                response = self.session.get(f"{self.base_url}/api/symbols/{exchange}", timeout=5)
                
                if response.status_code == 200:
                    data = response.json()
                    symbols = data.get('symbols', [])
                    self.log_test(f"GET /api/symbols/{exchange}", True, f"Found {len(symbols)} symbols")
                else:
                    self.log_test(f"GET /api/symbols/{exchange}", False, f"Status code: {response.status_code}")
            except Exception as e:
                self.log_test(f"GET /api/symbols/{exchange}", False, f"Error: {str(e)}")
    
    def run_all_tests(self):
        """Run all API tests"""
        print("🚀 Starting Strategy Builder API Tests...")
        print(f"📍 Testing server at: {self.base_url}")
        print("=" * 60)
        
        # Run all test categories
        self.test_health_endpoints()
        self.test_config_endpoints()
        self.test_ohlcv_endpoints()
        self.test_indicators_endpoints()
        self.test_marks_endpoints()
        self.test_strategy_endpoints()
        self.test_symbols_endpoint()
        
        # Print summary
        self.print_summary()
    
    def print_summary(self):
        """Print test summary"""
        print("\n" + "=" * 60)
        print("📊 TEST SUMMARY")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result['success'])
        failed_tests = total_tests - passed_tests
        
        print(f"Total Tests: {total_tests}")
        print(f"✅ Passed: {passed_tests}")
        print(f"❌ Failed: {failed_tests}")
        print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        if failed_tests > 0:
            print(f"\n❌ Failed Tests:")
            for result in self.test_results:
                if not result['success']:
                    print(f"  - {result['test']}: {result['details']}")
        
        print("\n🎯 Key Findings:")
        # Check Bollinger Bands configuration
        bb_config_test = next((r for r in self.test_results if 'config' in r['test'].lower()), None)
        if bb_config_test and bb_config_test['success']:
            print("  ✅ Bollinger Bands default length is set to 4")
        
        # Check API health
        health_test = next((r for r in self.test_results if 'api/health' in r['test']), None)
        if health_test and health_test['success']:
            print("  ✅ API health endpoint is working")
        
        print(f"\n📝 Detailed results saved to test_results.json")
        
        # Save detailed results to file
        with open('test_results.json', 'w') as f:
            json.dump(self.test_results, f, indent=2)

def main():
    """Main function"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Test Strategy Builder API')
    parser.add_argument('--url', default='http://localhost:8000', 
                       help='Base URL of the API server (default: http://localhost:8000)')
    parser.add_argument('--timeout', type=int, default=30,
                       help='Request timeout in seconds (default: 30)')
    
    args = parser.parse_args()
    
    # Create tester and run tests
    tester = StrategyBuilderAPITester(args.url)
    
    try:
        tester.run_all_tests()
    except KeyboardInterrupt:
        print("\n\n⚠️ Tests interrupted by user")
        tester.print_summary()
    except Exception as e:
        print(f"\n\n❌ Test execution failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
