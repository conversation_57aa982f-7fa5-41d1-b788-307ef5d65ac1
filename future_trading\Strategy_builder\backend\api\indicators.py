"""
Technical Indicators API endpoints
"""
from fastapi import APIRouter, HTTPException
# SQLAlchemy no longer used - using PyMySQL directly
from typing import List, Dict, Any, Optional
from pydantic import BaseModel
import logging

# Database operations now handled by PyMySQL operations classes
from backend.database.operations_pymysql import OHLCVOperations, IndicatorsOperations
from backend.core.technical_indicators import technical_indicators
from config.settings import validate_indicator

logger = logging.getLogger(__name__)
router = APIRouter()


class IndicatorConfig(BaseModel):
    name: str
    params: Optional[Dict[str, Any]] = {}


class IndicatorRequest(BaseModel):
    symbol: str
    timeframe: str
    indicators: List[IndicatorConfig]
    limit: Optional[int] = 500


class IndicatorResponse(BaseModel):
    symbol: str
    timeframe: str
    indicators: Dict[str, List[Dict[str, Any]]]
    count: int


@router.post("/calculate")
async def calculate_indicators(
    request: IndicatorRequest
) -> IndicatorResponse:
    """
    Calculate technical indicators for OHLCV data
    
    Args:
        request: Indicator calculation request
        
    Returns:
        Calculated indicator values
    """
    try:
        # Validate indicators
        for indicator_config in request.indicators:
            if not validate_indicator(indicator_config.name):
                raise HTTPException(
                    status_code=400, 
                    detail=f"Unsupported indicator: {indicator_config.name}"
                )
        
        # Get OHLCV data
        ohlcv_data = OHLCVOperations.get_ohlcv_data(
            db=db,
            symbol=request.symbol,
            timeframe=request.timeframe,
            limit=request.limit or 500
        )
        
        if not ohlcv_data:
            raise HTTPException(
                status_code=404, 
                detail=f"No OHLCV data found for {request.symbol} {request.timeframe}"
            )
        
        # Prepare indicator configurations
        indicators_config = []
        for indicator_config in request.indicators:
            # Get default params and merge with provided params
            default_params = technical_indicators.get_default_params(indicator_config.name)
            params = {**default_params, **(indicator_config.params or {})}
            
            indicators_config.append({
                'name': indicator_config.name.upper(),
                'params': params
            })
        
        # Calculate indicators
        indicators_data = technical_indicators.calculate_indicators(
            ohlcv_data=ohlcv_data,
            indicators_config=indicators_config
        )
        
        # Store indicators in database
        for indicator_name, data in indicators_data.items():
            try:
                # Find the corresponding config
                config = next(
                    (cfg for cfg in indicators_config if cfg['name'] == indicator_name),
                    {}
                )
                
                IndicatorOperations.insert_indicator_data(
                    db=db,
                    symbol=request.symbol,
                    timeframe=request.timeframe,
                    indicator_name=indicator_name,
                    indicator_params=config.get('params', {}),
                    data=data
                )
            except Exception as e:
                logger.warning(f"Failed to store {indicator_name} data: {e}")
        
        return IndicatorResponse(
            symbol=request.symbol,
            timeframe=request.timeframe,
            indicators=indicators_data,
            count=len(ohlcv_data)
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error calculating indicators: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/supported")
async def get_supported_indicators() -> Dict[str, Any]:
    """Get list of supported indicators and their default parameters"""
    try:
        supported = technical_indicators.get_supported_indicators()
        
        result = {}
        for indicator in supported:
            result[indicator] = {
                'name': indicator,
                'default_params': technical_indicators.get_default_params(indicator)
            }
        
        return {
            'indicators': result,
            'count': len(supported)
        }
        
    except Exception as e:
        logger.error(f"Error getting supported indicators: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/{symbol}/{timeframe}/{indicator_name}")
async def get_indicator_data(
    symbol: str,
    timeframe: str,
    indicator_name: str,
    limit: int = 500
) -> Dict[str, Any]:
    """Get cached indicator data"""
    try:
        if not validate_indicator(indicator_name):
            raise HTTPException(status_code=400, detail="Unsupported indicator")
        
        # This would implement getting cached indicator data
        # For now, return a placeholder
        return {
            'symbol': symbol,
            'timeframe': timeframe,
            'indicator_name': indicator_name.upper(),
            'message': 'Cached indicator data retrieval not yet implemented',
            'data': []
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting indicator data: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


class SnapshotRequest(BaseModel):
    symbol: str
    timeframe: str
    timestamp: str
    indicators: List[IndicatorConfig]


@router.post("/snapshot")
async def get_indicator_snapshot(
    request: SnapshotRequest
) -> Dict[str, Any]:
    """
    Get indicator values snapshot for a specific timestamp
    
    Args:
        request: Snapshot request with timestamp and indicators
        
    Returns:
        Indicator values at the specified timestamp
    """
    try:
        # Get OHLCV data around the timestamp
        from datetime import datetime, timedelta
        
        target_time = datetime.fromisoformat(request.timestamp.replace('Z', '+00:00'))
        start_time = target_time - timedelta(hours=24)  # Get enough data for indicators
        
        ohlcv_data = OHLCVOperations.get_ohlcv_data(
            db=db,
            symbol=request.symbol,
            timeframe=request.timeframe,
            start_time=start_time,
            end_time=target_time,
            limit=1000
        )
        
        if not ohlcv_data:
            raise HTTPException(
                status_code=404,
                detail=f"No OHLCV data found for {request.symbol} {request.timeframe}"
            )
        
        # Prepare indicator configurations
        indicators_config = []
        for indicator_config in request.indicators:
            default_params = technical_indicators.get_default_params(indicator_config.name)
            params = {**default_params, **(indicator_config.params or {})}
            
            indicators_config.append({
                'name': indicator_config.name.upper(),
                'params': params
            })
        
        # Get indicator snapshot
        snapshot = technical_indicators.get_indicator_snapshot(
            ohlcv_data=ohlcv_data,
            indicators_config=indicators_config,
            timestamp=request.timestamp
        )
        
        return {
            'symbol': request.symbol,
            'timeframe': request.timeframe,
            'timestamp': request.timestamp,
            'indicators': snapshot
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting indicator snapshot: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")
