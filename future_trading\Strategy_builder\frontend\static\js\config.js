/**
 * Configuration and Constants for Strategy Builder
 */

const CONFIG = {
    // API Configuration
    API_BASE_URL: window.location.origin + '/api',
    
    // Default Settings
    DEFAULT_SYMBOL: 'BTCUSDT',
    DEFAULT_TIMEFRAME: '1h',
    DEFAULT_EXCHANGE: 'binance',
    DEFAULT_CANDLES: 500,
    
    // Supported Timeframes
    TIMEFRAMES: ['1m', '3m', '5m', '15m', '30m', '1h', '2h', '4h', '1d', '1w'],
    
    // Supported Exchanges
    EXCHANGES: ['binance', 'mexc'],
    
    // Chart Configuration
    CHART_OPTIONS: {
        layout: {
            background: { color: '#1a1a1a' },
            textColor: '#ffffff',
        },
        grid: {
            vertLines: { color: '#2d2d2d' },
            horzLines: { color: '#2d2d2d' },
        },
        crosshair: {
            mode: LightweightCharts.CrosshairMode.Normal,
        },
        rightPriceScale: {
            borderColor: '#444444',
        },
        timeScale: {
            borderColor: '#444444',
            timeVisible: true,
            secondsVisible: false,
        },
        watermark: {
            visible: true,
            fontSize: 24,
            horzAlign: 'center',
            vertAlign: 'center',
            color: 'rgba(171, 71, 188, 0.3)',
            text: 'Strategy Builder',
        },
    },
    
    // Indicator Colors
    INDICATOR_COLORS: {
        RSI: '#ffc107',
        MACD: {
            macd: '#00d4aa',
            signal: '#dc3545',
            histogram: '#6c757d'
        },
        EMA: '#28a745',
        SMA: '#17a2b8',
        BB: {
            upper: '#fd7e14',
            middle: '#6f42c1',
            lower: '#fd7e14'
        },
        STOCH: {
            k: '#e83e8c',
            d: '#20c997'
        }
    },
    
    // Default Indicator Parameters
    DEFAULT_INDICATOR_PARAMS: {
        RSI: { length: 14 },
        MACD: { fast: 12, slow: 26, signal: 9 },
        EMA: { length: 20 },
        SMA: { length: 20 },
        BB: { length: 20, std: 2 },
        STOCH: { k: 14, d: 3, smooth_k: 3 }
    },
    
    // Trade Mark Colors
    TRADE_COLORS: {
        ENTRY_BUY: '#28a745',
        ENTRY_SELL: '#dc3545',
        EXIT: '#ffc107'
    },
    
    // Chart Dimensions
    CHART_HEIGHT: 400,
    INDICATOR_PANEL_HEIGHT: 150,
    
    // Update Intervals (milliseconds)
    UPDATE_INTERVALS: {
        REAL_TIME: 5000,
        SLOW: 30000
    },
    
    // API Endpoints
    ENDPOINTS: {
        OHLCV: '/ohlcv',
        INDICATORS: '/indicators',
        MARKS: '/marks',
        STRATEGY: '/strategy',
        CONFIG: '/config',
        HEALTH: '/health'
    },
    
    // Error Messages
    ERROR_MESSAGES: {
        NETWORK_ERROR: 'Network error. Please check your connection.',
        API_ERROR: 'API error. Please try again.',
        INVALID_SYMBOL: 'Invalid symbol format.',
        INVALID_TIMEFRAME: 'Invalid timeframe.',
        NO_DATA: 'No data available for the selected parameters.',
        SESSION_ERROR: 'Session error. Please create a new session.'
    },
    
    // Success Messages
    SUCCESS_MESSAGES: {
        DATA_LOADED: 'Data loaded successfully',
        INDICATORS_CALCULATED: 'Indicators calculated successfully',
        ENTRY_MARKED: 'Entry mark created successfully',
        EXIT_MARKED: 'Exit mark created successfully',
        SESSION_CREATED: 'Session created successfully',
        DATA_EXPORTED: 'Data exported successfully'
    },
    
    // Local Storage Keys
    STORAGE_KEYS: {
        CURRENT_SESSION: 'strategy_builder_current_session',
        USER_PREFERENCES: 'strategy_builder_preferences',
        INDICATOR_CONFIGS: 'strategy_builder_indicator_configs'
    },
    
    // Validation Rules
    VALIDATION: {
        SYMBOL_PATTERN: /^[A-Z]{3,10}USDT?$/,
        MIN_CANDLES: 50,
        MAX_CANDLES: 5000,
        MIN_PRICE: 0.000001,
        MAX_PRICE: 1000000
    }
};

/**
 * Utility Functions
 */
const UTILS = {
    /**
     * Format number with specified decimal places
     */
    formatNumber: (num, decimals = 2) => {
        if (num === null || num === undefined || isNaN(num)) return '0.00';
        return Number(num).toFixed(decimals);
    },
    
    /**
     * Format percentage
     */
    formatPercent: (num, decimals = 2) => {
        if (num === null || num === undefined || isNaN(num)) return '0.00%';
        return Number(num).toFixed(decimals) + '%';
    },
    
    /**
     * Format timestamp
     */
    formatTimestamp: (timestamp) => {
        if (!timestamp) return 'N/A';
        const date = new Date(timestamp);
        return date.toLocaleString();
    },
    
    /**
     * Validate symbol format
     */
    validateSymbol: (symbol) => {
        return CONFIG.VALIDATION.SYMBOL_PATTERN.test(symbol.toUpperCase());
    },
    
    /**
     * Validate timeframe
     */
    validateTimeframe: (timeframe) => {
        return CONFIG.TIMEFRAMES.includes(timeframe);
    },
    
    /**
     * Generate UUID
     */
    generateUUID: () => {
        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
            const r = Math.random() * 16 | 0;
            const v = c == 'x' ? r : (r & 0x3 | 0x8);
            return v.toString(16);
        });
    },
    
    /**
     * Debounce function
     */
    debounce: (func, wait) => {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },
    
    /**
     * Show loading overlay
     */
    showLoading: (text = 'Loading...') => {
        const overlay = document.getElementById('loading-overlay');
        const loadingText = overlay.querySelector('.loading-text');
        loadingText.textContent = text;
        overlay.style.display = 'flex';
    },
    
    /**
     * Hide loading overlay
     */
    hideLoading: () => {
        const overlay = document.getElementById('loading-overlay');
        overlay.style.display = 'none';
    },
    
    /**
     * Show notification
     */
    showNotification: (message, type = 'info') => {
        // Simple notification - could be enhanced with a proper notification library
        console.log(`[${type.toUpperCase()}] ${message}`);
        
        // Update status bar
        const statusElement = document.getElementById('data-status');
        if (statusElement) {
            statusElement.textContent = message;
            statusElement.className = `status-${type}`;
        }
    },
    
    /**
     * Get from local storage
     */
    getFromStorage: (key, defaultValue = null) => {
        try {
            const item = localStorage.getItem(key);
            return item ? JSON.parse(item) : defaultValue;
        } catch (error) {
            console.error('Error reading from localStorage:', error);
            return defaultValue;
        }
    },
    
    /**
     * Save to local storage
     */
    saveToStorage: (key, value) => {
        try {
            localStorage.setItem(key, JSON.stringify(value));
        } catch (error) {
            console.error('Error saving to localStorage:', error);
        }
    },
    
    /**
     * Remove from local storage
     */
    removeFromStorage: (key) => {
        try {
            localStorage.removeItem(key);
        } catch (error) {
            console.error('Error removing from localStorage:', error);
        }
    }
};

// Export for use in other modules
window.CONFIG = CONFIG;
window.UTILS = UTILS;
