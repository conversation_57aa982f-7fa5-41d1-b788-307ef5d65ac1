/* Strategy Builder CSS Styles */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #1a1a1a;
    color: #ffffff;
    overflow: hidden;
}

.app-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
}

/* Header */
.app-header {
    background-color: #2d2d2d;
    padding: 10px 20px;
    border-bottom: 1px solid #444;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.app-header h1 {
    color: #00d4aa;
    font-size: 24px;
}

.header-controls {
    display: flex;
    align-items: center;
    gap: 15px;
}

.session-info {
    display: flex;
    align-items: center;
    gap: 10px;
}

/* Main Content */
.main-content {
    display: flex;
    flex: 1;
    overflow: hidden;
}

/* Sidebar */
.sidebar {
    width: 300px;
    background-color: #2d2d2d;
    border-right: 1px solid #444;
    overflow-y: auto;
    padding: 20px;
}

.control-panel {
    display: flex;
    flex-direction: column;
    gap: 25px;
}

.control-group {
    background-color: #3a3a3a;
    padding: 15px;
    border-radius: 8px;
    border: 1px solid #555;
}

.control-group h3 {
    color: #00d4aa;
    margin-bottom: 15px;
    font-size: 16px;
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    color: #ccc;
    font-size: 14px;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 8px;
    background-color: #4a4a4a;
    border: 1px solid #666;
    border-radius: 4px;
    color: #fff;
    font-size: 14px;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: #00d4aa;
}

/* Buttons */
.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: background-color 0.2s;
}

.btn-primary {
    background-color: #00d4aa;
    color: #000;
}

.btn-primary:hover {
    background-color: #00b894;
}

.btn-secondary {
    background-color: #6c757d;
    color: #fff;
}

.btn-secondary:hover {
    background-color: #5a6268;
}

.btn-success {
    background-color: #28a745;
    color: #fff;
}

.btn-success:hover {
    background-color: #218838;
}

.btn-danger {
    background-color: #dc3545;
    color: #fff;
}

.btn-danger:hover {
    background-color: #c82333;
}

.btn-warning {
    background-color: #ffc107;
    color: #000;
}

.btn-warning:hover {
    background-color: #e0a800;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* Indicators */
.indicators-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-bottom: 15px;
}

.indicator-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 8px;
    background-color: #4a4a4a;
    border-radius: 4px;
}

.indicator-item input[type="checkbox"] {
    width: auto;
}

.indicator-item label {
    flex: 1;
    margin: 0;
    cursor: pointer;
}

.config-btn {
    background: none;
    border: none;
    color: #ccc;
    cursor: pointer;
    font-size: 16px;
    padding: 4px;
}

.config-btn:hover {
    color: #00d4aa;
}

/* Trade Marking */
.marking-controls {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-bottom: 15px;
}

.open-trades h4 {
    color: #ccc;
    margin-bottom: 10px;
    font-size: 14px;
}

#open-trades-list {
    max-height: 150px;
    overflow-y: auto;
}

.open-trade-item {
    background-color: #4a4a4a;
    padding: 8px;
    border-radius: 4px;
    margin-bottom: 5px;
    font-size: 12px;
}

/* Chart Container */
.chart-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    background-color: #1a1a1a;
}

.chart-header {
    background-color: #2d2d2d;
    padding: 10px 20px;
    border-bottom: 1px solid #444;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.chart-title {
    display: flex;
    align-items: center;
    gap: 15px;
}

.chart-title span {
    font-weight: 500;
}

#chart-symbol {
    color: #00d4aa;
    font-size: 18px;
}

#chart-timeframe {
    color: #ffc107;
}

#chart-status {
    color: #6c757d;
    font-size: 12px;
}

.chart-controls {
    display: flex;
    gap: 10px;
}

.chart-controls button {
    background-color: #4a4a4a;
    border: 1px solid #666;
    color: #fff;
    padding: 6px 10px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
}

.chart-controls button:hover {
    background-color: #5a5a5a;
}

.chart-area {
    flex: 1;
    background-color: #1a1a1a;
}

/* Indicator Panels */
.indicator-panels {
    display: flex;
    flex-direction: column;
}

.indicator-panel {
    height: 150px;
    border-top: 1px solid #444;
    background-color: #1a1a1a;
}

.panel-header {
    background-color: #2d2d2d;
    padding: 5px 15px;
    font-size: 12px;
    color: #ccc;
    border-bottom: 1px solid #444;
}

.indicator-chart {
    height: calc(100% - 30px);
}

/* Trade Sidebar */
.trade-sidebar {
    width: 300px;
    background-color: #2d2d2d;
    border-left: 1px solid #444;
    overflow-y: auto;
    padding: 20px;
}

.trade-log h3 {
    color: #00d4aa;
    margin-bottom: 15px;
}

.trade-stats {
    background-color: #3a3a3a;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    font-size: 14px;
}

.stat-label {
    color: #ccc;
}

.trade-list {
    max-height: 400px;
    overflow-y: auto;
}

.trade-item {
    background-color: #3a3a3a;
    padding: 10px;
    border-radius: 4px;
    margin-bottom: 10px;
    font-size: 12px;
}

.trade-item.profit {
    border-left: 4px solid #28a745;
}

.trade-item.loss {
    border-left: 4px solid #dc3545;
}

/* Status Bar */
.status-bar {
    background-color: #2d2d2d;
    padding: 8px 20px;
    border-top: 1px solid #444;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 12px;
    color: #ccc;
}

.status-left,
.status-right {
    display: flex;
    gap: 20px;
}

/* Modals */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
}

.modal-content {
    background-color: #2d2d2d;
    margin: 10% auto;
    padding: 0;
    border-radius: 8px;
    width: 400px;
    max-width: 90%;
    border: 1px solid #555;
}

.modal-header {
    padding: 15px 20px;
    border-bottom: 1px solid #444;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    color: #00d4aa;
    margin: 0;
}

.close {
    color: #ccc;
    font-size: 24px;
    font-weight: bold;
    cursor: pointer;
}

.close:hover {
    color: #fff;
}

.modal-body {
    padding: 20px;
}

.modal-footer {
    padding: 15px 20px;
    border-top: 1px solid #444;
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 2000;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #444;
    border-top: 4px solid #00d4aa;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.loading-text {
    color: #fff;
    margin-top: 15px;
    font-size: 16px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive */
@media (max-width: 1200px) {
    .sidebar,
    .trade-sidebar {
        width: 250px;
    }
}

@media (max-width: 768px) {
    .main-content {
        flex-direction: column;
    }
    
    .sidebar,
    .trade-sidebar {
        width: 100%;
        height: auto;
        max-height: 200px;
    }
}
