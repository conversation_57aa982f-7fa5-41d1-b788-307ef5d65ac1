-- Strategy Builder Database Schema
-- Phase 1 MVP

CREATE DATABASE IF NOT EXISTS strategy_buider;
USE strategy_buider;

-- 1. OHLCV Data Table
CREATE TABLE IF NOT EXISTS ohlcv_data (
    id INT AUTO_INCREMENT PRIMARY KEY,
    symbol VARCHAR(20) NOT NULL,
    timeframe VARCHAR(10) NOT NULL,
    timestamp DATETIME NOT NULL,
    open DECIMAL(20, 8) NOT NULL,
    high DECIMAL(20, 8) NOT NULL,
    low DECIMAL(20, 8) NOT NULL,
    close DECIMAL(20, 8) NOT NULL,
    volume DECIMAL(20, 8) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY unique_candle (symbol, timeframe, timestamp),
    INDEX idx_symbol_timeframe (symbol, timeframe),
    INDEX idx_timestamp (timestamp)
);

-- 2. Indicators Data Table
CREATE TABLE IF NOT EXISTS indicators_data (
    id INT AUTO_INCREMENT PRIMARY KEY,
    symbol VARCHAR(20) NOT NULL,
    timeframe VARCHAR(10) NOT NULL,
    timestamp DATETIME NOT NULL,
    indicator_name VARCHAR(50) NOT NULL,
    indicator_params JSON,
    value JSON NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY unique_indicator (symbol, timeframe, timestamp, indicator_name),
    INDEX idx_symbol_timeframe_indicator (symbol, timeframe, indicator_name),
    INDEX idx_timestamp (timestamp)
);

-- 3. Manual Marks Table
CREATE TABLE IF NOT EXISTS manual_marks (
    id INT AUTO_INCREMENT PRIMARY KEY,
    symbol VARCHAR(20) NOT NULL,
    timeframe VARCHAR(10) NOT NULL,
    mark_type ENUM('entry', 'exit') NOT NULL,
    entry_side ENUM('buy', 'sell') NULL,
    timestamp DATETIME NOT NULL,
    price DECIMAL(20, 8) NOT NULL,
    indicator_snapshot JSON,
    ohlcv_snapshot JSON,
    linked_trade_id INT NULL,
    session_id VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_symbol_timeframe (symbol, timeframe),
    INDEX idx_mark_type (mark_type),
    INDEX idx_linked_trade (linked_trade_id),
    INDEX idx_session (session_id)
);

-- 4. Strategy Log Table
CREATE TABLE IF NOT EXISTS strategy_log (
    strategy_id INT AUTO_INCREMENT PRIMARY KEY,
    symbol VARCHAR(20) NOT NULL,
    timeframe VARCHAR(10) NOT NULL,
    entry_id INT NOT NULL,
    exit_id INT NOT NULL,
    entry_side ENUM('buy', 'sell') NOT NULL,
    entry_price DECIMAL(20, 8) NOT NULL,
    exit_price DECIMAL(20, 8) NOT NULL,
    profit_pct DECIMAL(10, 4) NOT NULL,
    profit_absolute DECIMAL(20, 8),
    entry_timestamp DATETIME NOT NULL,
    exit_timestamp DATETIME NOT NULL,
    trade_duration_minutes INT,
    entry_ohlcv JSON,
    exit_ohlcv JSON,
    entry_indicator_snapshot JSON,
    exit_indicator_snapshot JSON,
    session_id VARCHAR(100),
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (entry_id) REFERENCES manual_marks(id),
    FOREIGN KEY (exit_id) REFERENCES manual_marks(id),
    INDEX idx_symbol_timeframe (symbol, timeframe),
    INDEX idx_profit (profit_pct),
    INDEX idx_session (session_id),
    INDEX idx_created_at (created_at)
);

-- 5. Strategy Sessions Table (for grouping related trades)
CREATE TABLE IF NOT EXISTS strategy_sessions (
    session_id VARCHAR(100) PRIMARY KEY,
    session_name VARCHAR(200),
    symbol VARCHAR(20) NOT NULL,
    timeframe VARCHAR(10) NOT NULL,
    start_date DATETIME,
    end_date DATETIME,
    total_trades INT DEFAULT 0,
    winning_trades INT DEFAULT 0,
    losing_trades INT DEFAULT 0,
    total_profit_pct DECIMAL(10, 4) DEFAULT 0,
    win_rate DECIMAL(5, 2) DEFAULT 0,
    avg_profit_per_trade DECIMAL(10, 4) DEFAULT 0,
    max_drawdown DECIMAL(10, 4) DEFAULT 0,
    indicators_used JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_symbol_timeframe (symbol, timeframe),
    INDEX idx_created_at (created_at)
);

-- Create indexes for better performance
CREATE INDEX idx_ohlcv_lookup ON ohlcv_data(symbol, timeframe, timestamp);
CREATE INDEX idx_indicators_lookup ON indicators_data(symbol, timeframe, indicator_name, timestamp);
CREATE INDEX idx_marks_lookup ON manual_marks(symbol, timeframe, timestamp, mark_type);
CREATE INDEX idx_strategy_performance ON strategy_log(symbol, timeframe, profit_pct, created_at);
