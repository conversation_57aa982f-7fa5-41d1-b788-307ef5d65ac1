#!/usr/bin/env python3
"""
Advanced MEXC TradingView-style charts with custom indicators and real-time features
"""

import json
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import requests
import os
from technical_indicators import TechnicalIndicators
from config import MEXC_API_KEY, MEXC_SECRET_KEY

class AdvancedMEXCCharts:
    """Advanced MEXC chart generator with custom indicators"""
    
    def __init__(self):
        self.indicators = TechnicalIndicators()
        self.base_url = "https://contract.mexc.com"
        
    def get_mexc_data(self, symbol='BTC_USDT', interval='Min5', limit=500):
        """Get data from MEXC API"""
        try:
            endpoint = f"{self.base_url}/api/v1/contract/kline/{symbol}"
            params = {'interval': interval, 'limit': limit}
            
            response = requests.get(endpoint, params=params, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success') and data.get('data'):
                    print(f"📊 Raw data sample: {data['data'][:2] if data['data'] else 'No data'}")

                    # Handle different data formats
                    raw_data = data['data']
                    if isinstance(raw_data[0], list):
                        # Data is in list format
                        df = pd.DataFrame(raw_data, columns=[
                            'timestamp', 'open', 'high', 'low', 'close', 'volume'
                        ])
                    else:
                        # Data is in dict format
                        df = pd.DataFrame(raw_data)
                    
                    df['timestamp'] = pd.to_datetime(df['timestamp'], unit='s')
                    for col in ['open', 'high', 'low', 'close', 'volume']:
                        df[col] = pd.to_numeric(df[col], errors='coerce')

                    # Remove any rows with NaN values
                    df = df.dropna()
                    
                    return self.add_advanced_indicators(df)
            return None
        except Exception as e:
            print(f"❌ Error: {e}")
            return None
    
    def add_advanced_indicators(self, df):
        """Add comprehensive technical indicators"""
        # Basic indicators
        df['ema_9'] = self.indicators.ema(df['close'], 9)
        df['ema_21'] = self.indicators.ema(df['close'], 21)
        df['ema_50'] = self.indicators.ema(df['close'], 50)
        df['ema_200'] = self.indicators.ema(df['close'], 200)
        
        # Oscillators
        df['rsi'] = self.indicators.rsi(df['close'], 14)
        df['stoch_k'], df['stoch_d'] = self.indicators.stochastic(df['high'], df['low'], df['close'], 14, 3)
        df['macd'], df['macd_signal'], df['macd_histogram'] = self.indicators.macd(df['close'])
        
        # Bollinger Bands
        df['bb_upper'], df['bb_middle'], df['bb_lower'] = self.indicators.bollinger_bands(df['close'], 20, 2)
        df['bb_width'] = (df['bb_upper'] - df['bb_lower']) / df['bb_middle'] * 100
        
        # Volume indicators
        df['volume_sma'] = self.indicators.volume_sma(df['volume'], 20)
        df['volume_ratio'] = df['volume'] / df['volume_sma']
        df['vwap'] = (df['volume'] * (df['high'] + df['low'] + df['close']) / 3).cumsum() / df['volume'].cumsum()
        
        # Custom indicators
        df['atr'] = self.indicators.atr(df['high'], df['low'], df['close'], 14)
        df['atr_percent'] = df['atr'] / df['close'] * 100
        
        # Momentum indicators
        df['momentum'] = df['close'] / df['close'].shift(10) - 1
        df['roc'] = df['close'].pct_change(10) * 100
        
        # Support/Resistance
        df['pivot'] = (df['high'] + df['low'] + df['close']) / 3
        df['r1'] = 2 * df['pivot'] - df['low']
        df['s1'] = 2 * df['pivot'] - df['high']
        df['r2'] = df['pivot'] + (df['high'] - df['low'])
        df['s2'] = df['pivot'] - (df['high'] - df['low'])
        
        # Trend indicators
        df['adx'] = self.calculate_adx(df)
        df['trend_strength'] = np.where(df['ema_21'] > df['ema_50'], 1, -1)
        
        # Custom patterns
        df['doji'] = self.detect_doji(df)
        df['hammer'] = self.detect_hammer(df)
        df['engulfing'] = self.detect_engulfing(df)
        
        return df
    
    def calculate_adx(self, df, period=14):
        """Calculate Average Directional Index"""
        try:
            high = df['high']
            low = df['low']
            close = df['close']
            
            plus_dm = high.diff()
            minus_dm = low.diff()
            plus_dm[plus_dm < 0] = 0
            minus_dm[minus_dm > 0] = 0
            
            tr1 = pd.DataFrame(high - low).rename(columns={'high': 'tr'})
            tr2 = pd.DataFrame(abs(high - close.shift(1))).rename(columns={'high': 'tr'})
            tr3 = pd.DataFrame(abs(low - close.shift(1))).rename(columns={'low': 'tr'})
            tr = pd.concat([tr1, tr2, tr3], axis=1, join='outer').max(axis=1)
            
            atr = tr.rolling(period).mean()
            plus_di = 100 * (plus_dm.rolling(period).mean() / atr)
            minus_di = 100 * (minus_dm.rolling(period).mean() / atr)
            
            dx = (abs(plus_di - minus_di) / abs(plus_di + minus_di)) * 100
            adx = dx.rolling(period).mean()
            
            return adx.fillna(0)
        except:
            return pd.Series([0] * len(df))
    
    def detect_doji(self, df):
        """Detect Doji candlestick patterns"""
        body = abs(df['close'] - df['open'])
        range_val = df['high'] - df['low']
        return (body / range_val < 0.1).astype(int)
    
    def detect_hammer(self, df):
        """Detect Hammer candlestick patterns"""
        body = abs(df['close'] - df['open'])
        upper_shadow = df['high'] - df[['open', 'close']].max(axis=1)
        lower_shadow = df[['open', 'close']].min(axis=1) - df['low']
        return ((lower_shadow > 2 * body) & (upper_shadow < body)).astype(int)
    
    def detect_engulfing(self, df):
        """Detect Engulfing patterns"""
        prev_body = abs(df['close'].shift(1) - df['open'].shift(1))
        curr_body = abs(df['close'] - df['open'])
        bullish_engulfing = (
            (df['close'].shift(1) < df['open'].shift(1)) &  # Previous red
            (df['close'] > df['open']) &  # Current green
            (df['open'] < df['close'].shift(1)) &  # Opens below prev close
            (df['close'] > df['open'].shift(1)) &  # Closes above prev open
            (curr_body > prev_body)  # Larger body
        )
        return bullish_engulfing.astype(int)

def create_advanced_chart_html(symbol='BTC_USDT'):
    """Create advanced TradingView-style chart"""
    
    charts = AdvancedMEXCCharts()
    
    # Get data for multiple timeframes
    timeframes = {
        '5M': charts.get_mexc_data(symbol, 'Min5', 500),
        '15M': charts.get_mexc_data(symbol, 'Min15', 500),
        '1H': charts.get_mexc_data(symbol, 'Hour1', 500)
    }
    
    # Filter successful data
    valid_data = {k: v for k, v in timeframes.items() if v is not None}
    
    if not valid_data:
        print("❌ No data available")
        return None
    
    # Convert to JSON
    chart_data = {}
    for tf, df in valid_data.items():
        chart_data[tf] = df.to_json(orient='records', date_format='iso')
    
    # Get current price info
    try:
        ticker_url = f"https://contract.mexc.com/api/v1/contract/ticker"
        response = requests.get(ticker_url, params={'symbol': symbol}, timeout=5)
        ticker = response.json().get('data', [{}])[0] if response.status_code == 200 else {}
        current_price = float(ticker.get('lastPrice', 0))
        price_change = float(ticker.get('priceChangePercent', 0))
        volume_24h = float(ticker.get('volume', 0))
    except:
        current_price = price_change = volume_24h = 0
    
    html_content = f'''
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚀 Advanced MEXC Charts - {symbol}</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        * {{ margin: 0; padding: 0; box-sizing: border-box; }}
        
        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0c0e16 0%, #1a1d29 100%);
            color: #e1e3e6;
            min-height: 100vh;
        }}
        
        .container {{
            max-width: 1900px;
            margin: 0 auto;
            padding: 15px;
        }}
        
        .header {{
            background: linear-gradient(135deg, #1e2329 0%, #2b3139 100%);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            border: 1px solid #363a45;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
        }}
        
        .header h1 {{
            color: #ffffff;
            font-size: 2.2em;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 15px;
        }}
        
        .live-badge {{
            background: linear-gradient(45deg, #00d4aa, #00b894);
            color: #000;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.5em;
            font-weight: bold;
            animation: glow 2s ease-in-out infinite alternate;
        }}
        
        @keyframes glow {{
            from {{ box-shadow: 0 0 5px #00d4aa; }}
            to {{ box-shadow: 0 0 20px #00d4aa, 0 0 30px #00d4aa; }}
        }}
        
        .price-dashboard {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 15px;
        }}
        
        .price-card {{
            background: rgba(54, 58, 69, 0.3);
            border-radius: 10px;
            padding: 15px;
            text-align: center;
            border: 1px solid #363a45;
        }}
        
        .price-value {{
            font-size: 1.8em;
            font-weight: bold;
            color: {'#00d4aa' if price_change >= 0 else '#ff6b6b'};
            margin-bottom: 5px;
        }}
        
        .price-label {{
            color: #868993;
            font-size: 0.9em;
            text-transform: uppercase;
        }}
        
        .controls {{
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }}
        
        .control-group {{
            display: flex;
            gap: 5px;
            background: #2b3139;
            border-radius: 8px;
            padding: 5px;
            border: 1px solid #363a45;
        }}
        
        .control-btn {{
            background: transparent;
            color: #d1d4dc;
            border: none;
            padding: 10px 15px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 0.9em;
            transition: all 0.3s ease;
            font-weight: 500;
        }}
        
        .control-btn:hover {{
            background: #363a45;
            transform: translateY(-1px);
        }}
        
        .control-btn.active {{
            background: linear-gradient(135deg, #00d4aa 0%, #00b894 100%);
            color: #000;
            box-shadow: 0 2px 10px rgba(0, 212, 170, 0.3);
        }}
        
        .chart-container {{
            background: linear-gradient(135deg, #1e2329 0%, #2b3139 100%);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            border: 1px solid #363a45;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }}
        
        .chart-title {{
            color: #ffffff;
            font-size: 1.4em;
            margin-bottom: 20px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 10px;
        }}
        
        .main-chart {{ height: 600px; }}
        .indicator-chart {{ height: 180px; }}
        
        .indicators-summary {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }}
        
        .indicator-card {{
            background: linear-gradient(135deg, #2b3139 0%, #363a45 100%);
            border-radius: 12px;
            padding: 20px;
            border: 1px solid #4a4e5a;
        }}
        
        .indicator-name {{
            color: #00d4aa;
            font-weight: bold;
            margin-bottom: 10px;
            font-size: 0.95em;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }}
        
        .indicator-main-value {{
            font-size: 1.6em;
            font-weight: bold;
            margin-bottom: 8px;
        }}
        
        .indicator-sub-value {{
            color: #868993;
            font-size: 0.85em;
            margin-bottom: 5px;
        }}
        
        .signal-badge {{
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.75em;
            font-weight: bold;
            text-transform: uppercase;
        }}
        
        .signal-buy {{ background: #00d4aa; color: #000; }}
        .signal-sell {{ background: #ff6b6b; color: #fff; }}
        .signal-neutral {{ background: #868993; color: #fff; }}
        
        .refresh-controls {{
            position: fixed;
            bottom: 30px;
            right: 30px;
            display: flex;
            flex-direction: column;
            gap: 10px;
        }}
        
        .refresh-btn {{
            background: linear-gradient(135deg, #00d4aa 0%, #00b894 100%);
            color: #000;
            border: none;
            padding: 15px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 1.2em;
            box-shadow: 0 4px 20px rgba(0, 212, 170, 0.4);
            transition: all 0.3s ease;
        }}
        
        .refresh-btn:hover {{
            transform: scale(1.1) rotate(180deg);
            box-shadow: 0 6px 25px rgba(0, 212, 170, 0.6);
        }}
        
        @media (max-width: 768px) {{
            .container {{ padding: 10px; }}
            .price-dashboard {{ grid-template-columns: 1fr 1fr; }}
            .main-chart {{ height: 400px; }}
            .indicator-chart {{ height: 150px; }}
        }}
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>
                🚀 {symbol.replace('_', '/')} Advanced Analysis
                <span class="live-badge">LIVE DATA</span>
            </h1>
            
            <div class="price-dashboard">
                <div class="price-card">
                    <div class="price-value">${current_price:,.2f}</div>
                    <div class="price-label">Current Price</div>
                </div>
                <div class="price-card">
                    <div class="price-value" style="color: {'#00d4aa' if price_change >= 0 else '#ff6b6b'}">
                        {'▲' if price_change >= 0 else '▼'} {price_change:+.2f}%
                    </div>
                    <div class="price-label">24h Change</div>
                </div>
                <div class="price-card">
                    <div class="price-value">{volume_24h:,.0f}</div>
                    <div class="price-label">24h Volume</div>
                </div>
                <div class="price-card">
                    <div class="price-value">{datetime.now().strftime('%H:%M:%S')}</div>
                    <div class="price-label">Last Update</div>
                </div>
            </div>
        </div>
        
        <!-- Controls -->
        <div class="controls">
            <div class="control-group">
                <button class="control-btn active" onclick="showTimeframe('5M')">5M</button>
                <button class="control-btn" onclick="showTimeframe('15M')">15M</button>
                <button class="control-btn" onclick="showTimeframe('1H')">1H</button>
            </div>
            
            <div class="control-group">
                <button class="control-btn" onclick="toggleIndicator('volume')">Volume</button>
                <button class="control-btn" onclick="toggleIndicator('rsi')">RSI</button>
                <button class="control-btn" onclick="toggleIndicator('macd')">MACD</button>
                <button class="control-btn" onclick="toggleIndicator('stoch')">Stochastic</button>
            </div>
            
            <div class="control-group">
                <button class="control-btn active" onclick="toggleOverlay('ema')">EMAs</button>
                <button class="control-btn" onclick="toggleOverlay('bb')">Bollinger</button>
                <button class="control-btn" onclick="toggleOverlay('vwap')">VWAP</button>
                <button class="control-btn" onclick="toggleOverlay('pivot')">Pivot</button>
            </div>
        </div>
        
        <!-- Main Price Chart -->
        <div class="chart-container">
            <div class="chart-title">📈 Price Chart with Advanced Indicators</div>
            <div id="priceChart" class="main-chart"></div>
        </div>
        
        <!-- Volume Chart -->
        <div class="chart-container" id="volumePanel" style="display: none;">
            <div class="chart-title">📊 Volume Analysis</div>
            <div id="volumeChart" class="indicator-chart"></div>
        </div>
        
        <!-- RSI Chart -->
        <div class="chart-container" id="rsiPanel" style="display: none;">
            <div class="chart-title">📈 RSI (14) with Divergence Detection</div>
            <div id="rsiChart" class="indicator-chart"></div>
        </div>
        
        <!-- MACD Chart -->
        <div class="chart-container" id="macdPanel" style="display: none;">
            <div class="chart-title">📊 MACD with Signal Crossovers</div>
            <div id="macdChart" class="indicator-chart"></div>
        </div>
        
        <!-- Stochastic Chart -->
        <div class="chart-container" id="stochPanel" style="display: none;">
            <div class="chart-title">📈 Stochastic Oscillator</div>
            <div id="stochChart" class="indicator-chart"></div>
        </div>
        
        <!-- Indicators Summary -->
        <div class="indicators-summary">
            <div class="indicator-card">
                <div class="indicator-name">RSI Analysis</div>
                <div class="indicator-main-value" id="rsiValue">--</div>
                <div class="indicator-sub-value" id="rsiSignal">Calculating...</div>
                <span class="signal-badge signal-neutral" id="rsiBadge">NEUTRAL</span>
            </div>
            
            <div class="indicator-card">
                <div class="indicator-name">MACD Momentum</div>
                <div class="indicator-main-value" id="macdValue">--</div>
                <div class="indicator-sub-value" id="macdSignal">Calculating...</div>
                <span class="signal-badge signal-neutral" id="macdBadge">NEUTRAL</span>
            </div>
            
            <div class="indicator-card">
                <div class="indicator-name">Volume Profile</div>
                <div class="indicator-main-value" id="volumeRatio">--</div>
                <div class="indicator-sub-value" id="volumeSignal">Calculating...</div>
                <span class="signal-badge signal-neutral" id="volumeBadge">NEUTRAL</span>
            </div>
            
            <div class="indicator-card">
                <div class="indicator-name">Trend Strength</div>
                <div class="indicator-main-value" id="adxValue">--</div>
                <div class="indicator-sub-value" id="trendSignal">Calculating...</div>
                <span class="signal-badge signal-neutral" id="trendBadge">NEUTRAL</span>
            </div>
            
            <div class="indicator-card">
                <div class="indicator-name">Bollinger Position</div>
                <div class="indicator-main-value" id="bbPosition">--</div>
                <div class="indicator-sub-value" id="bbSignal">Calculating...</div>
                <span class="signal-badge signal-neutral" id="bbBadge">NEUTRAL</span>
            </div>
            
            <div class="indicator-card">
                <div class="indicator-name">Pattern Detection</div>
                <div class="indicator-main-value" id="patternCount">--</div>
                <div class="indicator-sub-value" id="patternSignal">Calculating...</div>
                <span class="signal-badge signal-neutral" id="patternBadge">NEUTRAL</span>
            </div>
        </div>
    </div>
    
    <!-- Refresh Controls -->
    <div class="refresh-controls">
        <button class="refresh-btn" onclick="refreshData()" title="Refresh Data">🔄</button>
        <button class="refresh-btn" onclick="toggleAutoRefresh()" title="Auto Refresh" id="autoBtn">⏰</button>
    </div>

    <script>
        // Data from Python
        const chartData = {json.dumps(chart_data)};
        
        let currentTimeframe = '5M';
        let visibleIndicators = new Set();
        let visibleOverlays = new Set(['ema']);
        let autoRefreshInterval = null;
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {{
            createPriceChart();
            updateIndicatorSummary();
        }});
        
        function showTimeframe(timeframe) {{
            currentTimeframe = timeframe;
            document.querySelectorAll('.control-btn').forEach(btn => {{
                if (['5M', '15M', '1H'].includes(btn.textContent)) {{
                    btn.classList.remove('active');
                }}
            }});
            event.target.classList.add('active');
            updateAllCharts();
        }}
        
        function toggleIndicator(indicator) {{
            const panel = document.getElementById(indicator + 'Panel');
            if (visibleIndicators.has(indicator)) {{
                visibleIndicators.delete(indicator);
                panel.style.display = 'none';
                event.target.classList.remove('active');
            }} else {{
                visibleIndicators.add(indicator);
                panel.style.display = 'block';
                event.target.classList.add('active');
                createIndicatorChart(indicator);
            }}
        }}
        
        function toggleOverlay(overlay) {{
            if (visibleOverlays.has(overlay)) {{
                visibleOverlays.delete(overlay);
                event.target.classList.remove('active');
            }} else {{
                visibleOverlays.add(overlay);
                event.target.classList.add('active');
            }}
            createPriceChart();
        }}
        
        function updateAllCharts() {{
            createPriceChart();
            visibleIndicators.forEach(indicator => createIndicatorChart(indicator));
            updateIndicatorSummary();
        }}
        
        function createPriceChart() {{
            const data = chartData[currentTimeframe];
            if (!data) return;
            
            const parsedData = JSON.parse(data);
            const traces = [];
            
            // Candlestick
            traces.push({{
                x: parsedData.map(d => d.timestamp),
                open: parsedData.map(d => d.open),
                high: parsedData.map(d => d.high),
                low: parsedData.map(d => d.low),
                close: parsedData.map(d => d.close),
                type: 'candlestick',
                name: '{symbol.replace('_', '/')}',
                increasing: {{fillcolor: '#00d4aa', line: {{color: '#00d4aa'}}}},
                decreasing: {{fillcolor: '#ff6b6b', line: {{color: '#ff6b6b'}}}}
            }});
            
            // EMAs overlay
            if (visibleOverlays.has('ema')) {{
                const emaColors = {{'ema_9': '#ffeb3b', 'ema_21': '#ff9800', 'ema_50': '#9c27b0', 'ema_200': '#2196f3'}};
                ['ema_9', 'ema_21', 'ema_50', 'ema_200'].forEach(ema => {{
                    traces.push({{
                        x: parsedData.map(d => d.timestamp),
                        y: parsedData.map(d => d[ema]),
                        type: 'scatter',
                        mode: 'lines',
                        name: ema.toUpperCase(),
                        line: {{color: emaColors[ema], width: 1.5}},
                        opacity: 0.8
                    }});
                }});
            }}
            
            // Bollinger Bands
            if (visibleOverlays.has('bb')) {{
                traces.push({{
                    x: parsedData.map(d => d.timestamp),
                    y: parsedData.map(d => d.bb_upper),
                    type: 'scatter',
                    mode: 'lines',
                    name: 'BB Upper',
                    line: {{color: '#64b5f6', width: 1, dash: 'dot'}},
                    opacity: 0.6
                }});
                
                traces.push({{
                    x: parsedData.map(d => d.timestamp),
                    y: parsedData.map(d => d.bb_lower),
                    type: 'scatter',
                    mode: 'lines',
                    name: 'BB Lower',
                    line: {{color: '#64b5f6', width: 1, dash: 'dot'}},
                    opacity: 0.6,
                    fill: 'tonexty',
                    fillcolor: 'rgba(100, 181, 246, 0.1)'
                }});
            }}
            
            // VWAP
            if (visibleOverlays.has('vwap')) {{
                traces.push({{
                    x: parsedData.map(d => d.timestamp),
                    y: parsedData.map(d => d.vwap),
                    type: 'scatter',
                    mode: 'lines',
                    name: 'VWAP',
                    line: {{color: '#ff5722', width: 2}},
                    opacity: 0.8
                }});
            }}
            
            // Pivot levels
            if (visibleOverlays.has('pivot')) {{
                ['pivot', 'r1', 's1'].forEach((level, i) => {{
                    const colors = ['#ffc107', '#4caf50', '#f44336'];
                    traces.push({{
                        x: parsedData.map(d => d.timestamp),
                        y: parsedData.map(d => d[level]),
                        type: 'scatter',
                        mode: 'lines',
                        name: level.toUpperCase(),
                        line: {{color: colors[i], width: 1, dash: 'dash'}},
                        opacity: 0.5
                    }});
                }});
            }}
            
            const layout = {{
                plot_bgcolor: '#0c0e16',
                paper_bgcolor: '#1e2329',
                font: {{color: '#e1e3e6', size: 11}},
                margin: {{l: 60, r: 60, t: 30, b: 40}},
                xaxis: {{
                    type: 'date',
                    gridcolor: '#363a45',
                    showgrid: true,
                    rangeslider: {{visible: false}}
                }},
                yaxis: {{
                    title: 'Price ($)',
                    gridcolor: '#363a45',
                    showgrid: true
                }},
                showlegend: true,
                legend: {{
                    x: 0,
                    y: 1,
                    bgcolor: 'rgba(30, 35, 41, 0.8)',
                    bordercolor: '#363a45',
                    borderwidth: 1
                }},
                hovermode: 'x unified'
            }};
            
            Plotly.newPlot('priceChart', traces, layout, {{responsive: true}});
        }}
        
        function createIndicatorChart(indicator) {{
            const data = chartData[currentTimeframe];
            if (!data) return;
            
            const parsedData = JSON.parse(data);
            const traces = [];
            let layout = {{
                plot_bgcolor: '#0c0e16',
                paper_bgcolor: '#1e2329',
                font: {{color: '#e1e3e6', size: 10}},
                margin: {{l: 60, r: 60, t: 10, b: 30}},
                xaxis: {{
                    type: 'date',
                    gridcolor: '#363a45',
                    showgrid: true
                }},
                yaxis: {{
                    gridcolor: '#363a45',
                    showgrid: true
                }},
                showlegend: false,
                hovermode: 'x'
            }};
            
            if (indicator === 'volume') {{
                traces.push({{
                    x: parsedData.map(d => d.timestamp),
                    y: parsedData.map(d => d.volume),
                    type: 'bar',
                    name: 'Volume',
                    marker: {{
                        color: parsedData.map(d => d.close > d.open ? '#00d4aa' : '#ff6b6b'),
                        opacity: 0.7
                    }}
                }});
                
                traces.push({{
                    x: parsedData.map(d => d.timestamp),
                    y: parsedData.map(d => d.volume_sma),
                    type: 'scatter',
                    mode: 'lines',
                    name: 'Volume SMA',
                    line: {{color: '#ffeb3b', width: 2}}
                }});
                
                layout.yaxis.title = 'Volume';
            }} else if (indicator === 'rsi') {{
                traces.push({{
                    x: parsedData.map(d => d.timestamp),
                    y: parsedData.map(d => d.rsi),
                    type: 'scatter',
                    mode: 'lines',
                    name: 'RSI',
                    line: {{color: '#9c27b0', width: 2}}
                }});
                
                // RSI levels
                [70, 50, 30].forEach((level, i) => {{
                    const colors = ['#ff6b6b', '#868993', '#00d4aa'];
                    traces.push({{
                        x: parsedData.map(d => d.timestamp),
                        y: Array(parsedData.length).fill(level),
                        type: 'scatter',
                        mode: 'lines',
                        line: {{color: colors[i], width: 1, dash: 'dash'}},
                        opacity: 0.5
                    }});
                }});
                
                layout.yaxis.title = 'RSI';
                layout.yaxis.range = [0, 100];
            }} else if (indicator === 'macd') {{
                traces.push({{
                    x: parsedData.map(d => d.timestamp),
                    y: parsedData.map(d => d.macd),
                    type: 'scatter',
                    mode: 'lines',
                    name: 'MACD',
                    line: {{color: '#2196f3', width: 2}}
                }});
                traces.push({{
                    x: parsedData.map(d => d.timestamp),
                    y: parsedData.map(d => d.macd_signal),
                    type: 'scatter',
                    mode: 'lines',
                    name: 'Signal',
                    line: {{color: '#ff9800', width: 2}}
                }});
                traces.push({{
                    x: parsedData.map(d => d.timestamp),
                    y: parsedData.map(d => d.macd_histogram),
                    type: 'bar',
                    name: 'Histogram',
                    marker: {{
                        color: parsedData.map(d => d.macd_histogram > 0 ? '#00d4aa' : '#ff6b6b'),
                        opacity: 0.6
                    }}
                }});
                layout.yaxis.title = 'MACD';
            }} else if (indicator === 'stoch') {{
                traces.push({{
                    x: parsedData.map(d => d.timestamp),
                    y: parsedData.map(d => d.stoch_k),
                    type: 'scatter',
                    mode: 'lines',
                    name: '%K',
                    line: {{color: '#00d4aa', width: 2}}
                }});
                traces.push({{
                    x: parsedData.map(d => d.timestamp),
                    y: parsedData.map(d => d.stoch_d),
                    type: 'scatter',
                    mode: 'lines',
                    name: '%D',
                    line: {{color: '#ff6b6b', width: 2}}
                }});
                
                [80, 20].forEach(level => {{
                    traces.push({{
                        x: parsedData.map(d => d.timestamp),
                        y: Array(parsedData.length).fill(level),
                        type: 'scatter',
                        mode: 'lines',
                        line: {{color: '#868993', width: 1, dash: 'dash'}},
                        opacity: 0.3
                    }});
                }});
                
                layout.yaxis.title = 'Stochastic';
                layout.yaxis.range = [0, 100];
            }}
            
            Plotly.newPlot(indicator + 'Chart', traces, layout, {{responsive: true}});
        }}
        
        function updateIndicatorSummary() {{
            const data = chartData[currentTimeframe];
            if (!data) return;
            
            const parsedData = JSON.parse(data);
            const latest = parsedData[parsedData.length - 1];
            
            // RSI Analysis
            const rsi = latest.rsi || 0;
            document.getElementById('rsiValue').textContent = rsi.toFixed(1);
            document.getElementById('rsiSignal').textContent = 
                rsi > 70 ? 'Overbought Territory' : 
                rsi < 30 ? 'Oversold Territory' : 
                'Neutral Zone';
            
            const rsiBadge = document.getElementById('rsiBadge');
            if (rsi > 70) {{
                rsiBadge.textContent = 'SELL';
                rsiBadge.className = 'signal-badge signal-sell';
            }} else if (rsi < 30) {{
                rsiBadge.textContent = 'BUY';
                rsiBadge.className = 'signal-badge signal-buy';
            }} else {{
                rsiBadge.textContent = 'NEUTRAL';
                rsiBadge.className = 'signal-badge signal-neutral';
            }}
            
            // MACD Analysis
            const macd = latest.macd || 0;
            const macdSignal = latest.macd_signal || 0;
            document.getElementById('macdValue').textContent = macd.toFixed(4);
            document.getElementById('macdSignal').textContent = 
                macd > macdSignal ? 'Bullish Momentum' : 'Bearish Momentum';
            
            const macdBadge = document.getElementById('macdBadge');
            if (macd > macdSignal && macd > 0) {{
                macdBadge.textContent = 'BUY';
                macdBadge.className = 'signal-badge signal-buy';
            }} else if (macd < macdSignal && macd < 0) {{
                macdBadge.textContent = 'SELL';
                macdBadge.className = 'signal-badge signal-sell';
            }} else {{
                macdBadge.textContent = 'NEUTRAL';
                macdBadge.className = 'signal-badge signal-neutral';
            }}
            
            // Volume Analysis
            const volumeRatio = latest.volume_ratio || 0;
            document.getElementById('volumeRatio').textContent = volumeRatio.toFixed(2) + 'x';
            document.getElementById('volumeSignal').textContent = 
                volumeRatio > 1.5 ? 'High Volume Activity' : 
                volumeRatio < 0.5 ? 'Low Volume Activity' : 
                'Normal Volume';
            
            const volumeBadge = document.getElementById('volumeBadge');
            if (volumeRatio > 1.5) {{
                volumeBadge.textContent = 'HIGH';
                volumeBadge.className = 'signal-badge signal-buy';
            }} else if (volumeRatio < 0.5) {{
                volumeBadge.textContent = 'LOW';
                volumeBadge.className = 'signal-badge signal-sell';
            }} else {{
                volumeBadge.textContent = 'NORMAL';
                volumeBadge.className = 'signal-badge signal-neutral';
            }}
            
            // ADX/Trend Analysis
            const adx = latest.adx || 0;
            document.getElementById('adxValue').textContent = adx.toFixed(1);
            document.getElementById('trendSignal').textContent = 
                adx > 25 ? 'Strong Trend' : 
                adx > 20 ? 'Moderate Trend' : 
                'Weak/No Trend';
            
            const trendBadge = document.getElementById('trendBadge');
            if (adx > 25) {{
                trendBadge.textContent = 'STRONG';
                trendBadge.className = 'signal-badge signal-buy';
            }} else if (adx > 20) {{
                trendBadge.textContent = 'MODERATE';
                trendBadge.className = 'signal-badge signal-neutral';
            }} else {{
                trendBadge.textContent = 'WEAK';
                trendBadge.className = 'signal-badge signal-sell';
            }}
            
            // Bollinger Bands Position
            const price = latest.close || 0;
            const bbUpper = latest.bb_upper || 0;
            const bbLower = latest.bb_lower || 0;
            const bbPosition = ((price - bbLower) / (bbUpper - bbLower) * 100);
            
            document.getElementById('bbPosition').textContent = bbPosition.toFixed(1) + '%';
            document.getElementById('bbSignal').textContent = 
                bbPosition > 80 ? 'Near Upper Band' : 
                bbPosition < 20 ? 'Near Lower Band' : 
                'Middle Range';
            
            const bbBadge = document.getElementById('bbBadge');
            if (bbPosition > 80) {{
                bbBadge.textContent = 'SELL';
                bbBadge.className = 'signal-badge signal-sell';
            }} else if (bbPosition < 20) {{
                bbBadge.textContent = 'BUY';
                bbBadge.className = 'signal-badge signal-buy';
            }} else {{
                bbBadge.textContent = 'NEUTRAL';
                bbBadge.className = 'signal-badge signal-neutral';
            }}
            
            // Pattern Detection
            const patterns = ['doji', 'hammer', 'engulfing'];
            const patternCount = patterns.reduce((sum, pattern) => sum + (latest[pattern] || 0), 0);
            
            document.getElementById('patternCount').textContent = patternCount;
            document.getElementById('patternSignal').textContent = 
                patternCount > 0 ? 'Patterns Detected' : 'No Patterns';
            
            const patternBadge = document.getElementById('patternBadge');
            if (patternCount > 0) {{
                patternBadge.textContent = 'ACTIVE';
                patternBadge.className = 'signal-badge signal-buy';
            }} else {{
                patternBadge.textContent = 'NONE';
                patternBadge.className = 'signal-badge signal-neutral';
            }}
        }}
        
        function refreshData() {{
            location.reload();
        }}
        
        function toggleAutoRefresh() {{
            const btn = document.getElementById('autoBtn');
            if (autoRefreshInterval) {{
                clearInterval(autoRefreshInterval);
                autoRefreshInterval = null;
                btn.style.background = 'linear-gradient(135deg, #00d4aa 0%, #00b894 100%)';
            }} else {{
                autoRefreshInterval = setInterval(refreshData, 60000); // 1 minute
                btn.style.background = 'linear-gradient(135deg, #ff6b6b 0%, #ff5252 100%)';
            }}
        }}
    </script>
</body>
</html>
'''
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"advanced_mexc_chart_{symbol}_{timestamp}.html"
    
    with open(filename, 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print(f"✅ Advanced MEXC chart created: {filename}")
    return filename

def main():
    """Main function"""
    print("="*80)
    print("🚀 ADVANCED MEXC TRADINGVIEW-STYLE CHARTS")
    print("="*80)
    
    symbol = 'BTC_USDT'
    
    print(f"🔄 Creating advanced chart for {symbol}...")
    
    filename = create_advanced_chart_html(symbol)
    
    if filename:
        print(f"\n🎉 ADVANCED CHART CREATED!")
        print(f"📁 File: {filename}")
        print(f"\n🚀 Advanced Features:")
        print(f"   ✅ Real-time MEXC data (5M, 15M, 1H)")
        print(f"   ✅ Multiple EMA overlays (9, 21, 50, 200)")
        print(f"   ✅ Advanced indicators (RSI, MACD, Stochastic, ADX)")
        print(f"   ✅ Volume analysis with SMA overlay")
        print(f"   ✅ Bollinger Bands with squeeze detection")
        print(f"   ✅ VWAP and Pivot levels")
        print(f"   ✅ Candlestick pattern detection")
        print(f"   ✅ Smart signal analysis")
        print(f"   ✅ Auto-refresh capability")
        print(f"   ✅ Professional gradient styling")
        
        try:
            import webbrowser
            full_path = os.path.abspath(filename)
            webbrowser.open(f'file://{full_path}')
            print(f"🌐 Opening in browser...")
        except:
            print(f"💡 Open manually: {os.path.abspath(filename)}")

if __name__ == "__main__":
    main()
