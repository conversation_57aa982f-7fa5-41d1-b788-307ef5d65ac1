# Strategy Builder - Windows Compatible Requirements
# Use this file if you have issues with the main requirements.txt

# Core FastAPI Backend
fastapi
uvicorn[standard]
pydantic
python-multipart

# Database
mysql-connector-python
SQLAlchemy

# Data Processing (Windows compatible)
pandas
numpy
pandas-ta

# HTTP Requests
requests

# Utilities
python-dotenv
python-dateutil

# Windows Build Tools
setuptools
wheel
