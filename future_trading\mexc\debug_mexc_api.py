#!/usr/bin/env python3
"""
Debug MEXC API to understand the data format
"""

import requests
import json
import pandas as pd

def debug_mexc_api():
    """Debug MEXC API response"""
    
    symbol = 'BTC_USDT'
    interval = 'Min5'
    limit = 10
    
    url = f"https://contract.mexc.com/api/v1/contract/kline/{symbol}"
    params = {'interval': interval, 'limit': limit}
    
    print(f"🔍 Debugging MEXC API...")
    print(f"URL: {url}")
    print(f"Params: {params}")
    
    try:
        response = requests.get(url, params=params, timeout=10)
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"Response Keys: {list(data.keys())}")
            print(f"Success: {data.get('success')}")
            print(f"Code: {data.get('code')}")
            print(f"Message: {data.get('message', 'No message')}")
            
            if data.get('data'):
                klines = data['data']
                print(f"Data Length: {len(klines)}")
                print(f"First Item Type: {type(klines[0])}")
                print(f"First Item: {klines[0]}")
                
                if len(klines) > 1:
                    print(f"Second Item: {klines[1]}")
                
                # Try to create DataFrame
                try:
                    if isinstance(klines[0], list):
                        print("Data is in list format")
                        df = pd.DataFrame(klines, columns=[
                            'timestamp', 'open', 'high', 'low', 'close', 'volume'
                        ])
                    else:
                        print("Data is in dict format")
                        df = pd.DataFrame(klines)
                    
                    print(f"DataFrame shape: {df.shape}")
                    print(f"DataFrame columns: {list(df.columns)}")
                    print(f"DataFrame head:")
                    print(df.head())
                    
                    # Check data types
                    print(f"Data types:")
                    print(df.dtypes)
                    
                except Exception as e:
                    print(f"Error creating DataFrame: {e}")
            else:
                print("No data in response")
        else:
            print(f"HTTP Error: {response.text}")
            
    except Exception as e:
        print(f"Exception: {e}")

def test_alternative_endpoints():
    """Test alternative MEXC endpoints"""
    
    print("\n" + "="*50)
    print("Testing alternative endpoints...")
    
    # Try different endpoints
    endpoints = [
        "https://contract.mexc.com/api/v1/contract/kline/BTC_USDT",
        "https://www.mexc.com/open/api/v2/market/kline",
        "https://api.mexc.com/api/v3/klines"
    ]
    
    for endpoint in endpoints:
        print(f"\n🔍 Testing: {endpoint}")
        
        try:
            if "v2/market/kline" in endpoint:
                params = {'symbol': 'BTC_USDT', 'interval': '5m', 'limit': 10}
            elif "v3/klines" in endpoint:
                params = {'symbol': 'BTCUSDT', 'interval': '5m', 'limit': 10}
            else:
                params = {'interval': 'Min5', 'limit': 10}
            
            response = requests.get(endpoint, params=params, timeout=10)
            print(f"Status: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"Response type: {type(data)}")
                
                if isinstance(data, dict):
                    print(f"Keys: {list(data.keys())}")
                    if 'data' in data:
                        print(f"Data length: {len(data['data']) if data['data'] else 0}")
                        if data['data']:
                            print(f"Sample: {data['data'][0]}")
                elif isinstance(data, list):
                    print(f"List length: {len(data)}")
                    if data:
                        print(f"Sample: {data[0]}")
            else:
                print(f"Error: {response.text[:200]}")
                
        except Exception as e:
            print(f"Exception: {e}")

def create_sample_chart():
    """Create a chart with sample data if API fails"""
    
    print("\n" + "="*50)
    print("Creating sample chart with mock data...")
    
    # Generate sample data
    import numpy as np
    from datetime import datetime, timedelta
    
    dates = pd.date_range(start=datetime.now() - timedelta(hours=24), 
                         end=datetime.now(), freq='5min')
    
    np.random.seed(42)
    base_price = 95000
    
    data = []
    for i, date in enumerate(dates):
        price = base_price + np.random.normal(0, 500) + (i * 2)
        open_price = price + np.random.normal(0, 100)
        high_price = max(open_price, price) + abs(np.random.normal(0, 200))
        low_price = min(open_price, price) - abs(np.random.normal(0, 200))
        close_price = price + np.random.normal(0, 100)
        volume = abs(np.random.normal(1000, 300))
        
        data.append({
            'timestamp': date.isoformat(),
            'open': max(1, open_price),
            'high': max(1, high_price),
            'low': max(1, low_price),
            'close': max(1, close_price),
            'volume': max(1, volume)
        })
    
    # Create simple HTML chart
    chart_data = json.dumps(data)
    
    html_content = f'''
<!DOCTYPE html>
<html>
<head>
    <title>📊 MEXC Sample Chart</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        body {{ 
            font-family: Arial, sans-serif; 
            background: #1e222d; 
            color: #d1d4dc; 
            margin: 20px;
        }}
        .container {{ max-width: 1200px; margin: 0 auto; }}
        .chart {{ height: 600px; margin: 20px 0; }}
        h1 {{ text-align: center; color: #00d4aa; }}
    </style>
</head>
<body>
    <div class="container">
        <h1>📊 MEXC Sample Chart (Mock Data)</h1>
        <div id="chart" class="chart"></div>
    </div>
    
    <script>
        const data = {chart_data};
        
        const trace = {{
            x: data.map(d => d.timestamp),
            open: data.map(d => d.open),
            high: data.map(d => d.high),
            low: data.map(d => d.low),
            close: data.map(d => d.close),
            type: 'candlestick',
            name: 'BTC/USDT',
            increasing: {{fillcolor: '#00d4aa', line: {{color: '#00d4aa'}}}},
            decreasing: {{fillcolor: '#ff6b6b', line: {{color: '#ff6b6b'}}}}
        }};
        
        const layout = {{
            plot_bgcolor: '#1e222d',
            paper_bgcolor: '#2a2e39',
            font: {{color: '#d1d4dc'}},
            title: 'BTC/USDT Sample Chart',
            xaxis: {{
                type: 'date',
                gridcolor: '#363a45',
                showgrid: true,
                rangeslider: {{visible: false}}
            }},
            yaxis: {{
                title: 'Price ($)',
                gridcolor: '#363a45',
                showgrid: true
            }}
        }};
        
        Plotly.newPlot('chart', [trace], layout, {{responsive: true}});
    </script>
</body>
</html>
'''
    
    filename = f"mexc_sample_chart_{datetime.now().strftime('%Y%m%d_%H%M%S')}.html"
    
    with open(filename, 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print(f"✅ Sample chart created: {filename}")
    
    try:
        import webbrowser
        import os
        full_path = os.path.abspath(filename)
        webbrowser.open(f'file://{full_path}')
        print(f"🌐 Opening in browser...")
    except:
        print(f"💡 Open manually: {os.path.abspath(filename)}")

def main():
    """Main function"""
    print("="*80)
    print("🔍 MEXC API DEBUG & SAMPLE CHART")
    print("="*80)
    
    debug_mexc_api()
    test_alternative_endpoints()
    create_sample_chart()

if __name__ == "__main__":
    main()
