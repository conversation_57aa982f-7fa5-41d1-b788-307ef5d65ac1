"""
PyMySQL Database Connection and Operations
Simple database layer using PyMySQL instead of SQLAlchemy
"""
import pymysql
import logging
from contextlib import contextmanager
from typing import Dict, List, Any, Optional
import json
from datetime import datetime

# Configure logging
logger = logging.getLogger(__name__)

# Database Configuration
DB_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': '@Oppa121089',
    'database': 'strategy_buider',
    'charset': 'utf8mb4',
    'autocommit': True
}


class DatabaseManager:
    """Simple database manager using PyMySQL"""
    
    def __init__(self):
        self.config = DB_CONFIG
    
    @contextmanager
    def get_connection(self):
        """Get database connection with context manager"""
        connection = None
        try:
            connection = pymysql.connect(**self.config)
            yield connection
        except Exception as e:
            logger.error(f"Database connection error: {e}")
            if connection:
                connection.rollback()
            raise
        finally:
            if connection:
                connection.close()
    
    def test_connection(self) -> bool:
        """Test database connection"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT 1")
                result = cursor.fetchone()
                cursor.close()
                logger.info("Database connection successful")
                return True
        except Exception as e:
            logger.error(f"Database connection failed: {e}")
            return False
    
    def get_health_check(self) -> Dict[str, Any]:
        """Get database health information"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT VERSION()")
                version = cursor.fetchone()[0]
                cursor.close()
                
                return {
                    "status": "healthy",
                    "database": "mysql",
                    "version": version,
                    "url": f"{self.config['host']}:{self.config.get('port', 3306)}/{self.config['database']}"
                }
        except Exception as e:
            logger.error(f"Health check failed: {e}")
            return {
                "status": "unhealthy",
                "error": str(e)
            }
    
    def execute_query(self, query: str, params: tuple = None) -> List[Dict[str, Any]]:
        """Execute SELECT query and return results as list of dictionaries"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor(pymysql.cursors.DictCursor)
                cursor.execute(query, params)
                results = cursor.fetchall()
                cursor.close()
                return results
        except Exception as e:
            logger.error(f"Query execution failed: {e}")
            raise
    
    def execute_insert(self, query: str, params: tuple = None) -> int:
        """Execute INSERT query and return last insert ID"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(query, params)
                last_id = cursor.lastrowid
                cursor.close()
                return last_id
        except Exception as e:
            logger.error(f"Insert execution failed: {e}")
            raise
    
    def execute_update(self, query: str, params: tuple = None) -> int:
        """Execute UPDATE/DELETE query and return affected rows"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(query, params)
                affected_rows = cursor.rowcount
                cursor.close()
                return affected_rows
        except Exception as e:
            logger.error(f"Update execution failed: {e}")
            raise
    
    def execute_many(self, query: str, params_list: List[tuple]) -> int:
        """Execute query with multiple parameter sets"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.executemany(query, params_list)
                affected_rows = cursor.rowcount
                cursor.close()
                return affected_rows
        except Exception as e:
            logger.error(f"Batch execution failed: {e}")
            raise


# Global database manager instance
db_manager = DatabaseManager()


# OHLCV Operations
class OHLCVOperations:
    """OHLCV data operations"""
    
    @staticmethod
    def insert_ohlcv_data(symbol: str, timeframe: str, data: List[Dict[str, Any]]) -> int:
        """Insert OHLCV data"""
        query = """
        INSERT INTO ohlcv_data (symbol, timeframe, timestamp, open, high, low, close, volume)
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
        ON DUPLICATE KEY UPDATE
        open = VALUES(open), high = VALUES(high), low = VALUES(low), 
        close = VALUES(close), volume = VALUES(volume)
        """
        
        params_list = []
        for candle in data:
            params_list.append((
                symbol, timeframe, candle['timestamp'],
                candle['open'], candle['high'], candle['low'],
                candle['close'], candle['volume']
            ))
        
        return db_manager.execute_many(query, params_list)
    
    @staticmethod
    def get_ohlcv_data(symbol: str, timeframe: str, limit: int = 500) -> List[Dict[str, Any]]:
        """Get OHLCV data"""
        query = """
        SELECT * FROM ohlcv_data 
        WHERE symbol = %s AND timeframe = %s 
        ORDER BY timestamp DESC 
        LIMIT %s
        """
        return db_manager.execute_query(query, (symbol, timeframe, limit))
    
    @staticmethod
    def get_latest_timestamp(symbol: str, timeframe: str) -> Optional[datetime]:
        """Get latest timestamp for symbol/timeframe"""
        query = """
        SELECT MAX(timestamp) as latest_timestamp 
        FROM ohlcv_data 
        WHERE symbol = %s AND timeframe = %s
        """
        result = db_manager.execute_query(query, (symbol, timeframe))
        return result[0]['latest_timestamp'] if result and result[0]['latest_timestamp'] else None


# Indicators Operations
class IndicatorsOperations:
    """Technical indicators operations"""
    
    @staticmethod
    def insert_indicators_data(symbol: str, timeframe: str, data: List[Dict[str, Any]]) -> int:
        """Insert indicators data"""
        query = """
        INSERT INTO indicators_data (symbol, timeframe, timestamp, indicator_name, indicator_params, value)
        VALUES (%s, %s, %s, %s, %s, %s)
        ON DUPLICATE KEY UPDATE
        indicator_params = VALUES(indicator_params), value = VALUES(value)
        """
        
        params_list = []
        for indicator_data in data:
            params_list.append((
                symbol, timeframe, indicator_data['timestamp'],
                indicator_data['indicator_name'],
                json.dumps(indicator_data.get('params', {})),
                json.dumps(indicator_data['value'])
            ))
        
        return db_manager.execute_many(query, params_list)
    
    @staticmethod
    def get_indicators_data(symbol: str, timeframe: str, indicator_name: str = None, limit: int = 500) -> List[Dict[str, Any]]:
        """Get indicators data"""
        if indicator_name:
            query = """
            SELECT * FROM indicators_data 
            WHERE symbol = %s AND timeframe = %s AND indicator_name = %s
            ORDER BY timestamp DESC 
            LIMIT %s
            """
            params = (symbol, timeframe, indicator_name, limit)
        else:
            query = """
            SELECT * FROM indicators_data 
            WHERE symbol = %s AND timeframe = %s 
            ORDER BY timestamp DESC 
            LIMIT %s
            """
            params = (symbol, timeframe, limit)
        
        results = db_manager.execute_query(query, params)
        
        # Parse JSON fields
        for result in results:
            if result.get('indicator_params'):
                result['indicator_params'] = json.loads(result['indicator_params'])
            if result.get('value'):
                result['value'] = json.loads(result['value'])
        
        return results


# Manual Marks Operations
class ManualMarksOperations:
    """Manual trade marks operations"""
    
    @staticmethod
    def insert_manual_mark(mark_data: Dict[str, Any]) -> str:
        """Insert manual mark"""
        query = """
        INSERT INTO manual_marks (id, symbol, timeframe, timestamp, price, mark_type, 
                                entry_side, linked_trade_id, indicator_snapshot, 
                                ohlcv_snapshot, session_id)
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """
        
        params = (
            mark_data['id'], mark_data['symbol'], mark_data['timeframe'],
            mark_data['timestamp'], mark_data['price'], mark_data['mark_type'],
            mark_data.get('entry_side'), mark_data.get('linked_trade_id'),
            json.dumps(mark_data.get('indicator_snapshot', {})),
            json.dumps(mark_data.get('ohlcv_snapshot', {})),
            mark_data.get('session_id')
        )
        
        db_manager.execute_insert(query, params)
        return mark_data['id']
    
    @staticmethod
    def get_manual_marks(symbol: str, timeframe: str, session_id: str = None) -> List[Dict[str, Any]]:
        """Get manual marks"""
        if session_id:
            query = """
            SELECT * FROM manual_marks 
            WHERE symbol = %s AND timeframe = %s AND session_id = %s
            ORDER BY timestamp DESC
            """
            params = (symbol, timeframe, session_id)
        else:
            query = """
            SELECT * FROM manual_marks 
            WHERE symbol = %s AND timeframe = %s 
            ORDER BY timestamp DESC
            """
            params = (symbol, timeframe)
        
        results = db_manager.execute_query(query, params)
        
        # Parse JSON fields
        for result in results:
            if result.get('indicator_snapshot'):
                result['indicator_snapshot'] = json.loads(result['indicator_snapshot'])
            if result.get('ohlcv_snapshot'):
                result['ohlcv_snapshot'] = json.loads(result['ohlcv_snapshot'])
        
        return results


# Strategy Sessions Operations
class StrategySessionsOperations:
    """Strategy sessions operations"""
    
    @staticmethod
    def create_session(session_data: Dict[str, Any]) -> str:
        """Create new strategy session"""
        query = """
        INSERT INTO strategy_sessions (session_id, session_name, symbol, timeframe)
        VALUES (%s, %s, %s, %s)
        """
        
        params = (
            session_data['session_id'], session_data.get('session_name'),
            session_data['symbol'], session_data['timeframe']
        )
        
        db_manager.execute_insert(query, params)
        return session_data['session_id']
    
    @staticmethod
    def get_sessions(symbol: str = None, timeframe: str = None) -> List[Dict[str, Any]]:
        """Get strategy sessions"""
        if symbol and timeframe:
            query = """
            SELECT * FROM strategy_sessions 
            WHERE symbol = %s AND timeframe = %s 
            ORDER BY created_at DESC
            """
            params = (symbol, timeframe)
        else:
            query = "SELECT * FROM strategy_sessions ORDER BY created_at DESC"
            params = None
        
        return db_manager.execute_query(query, params)
