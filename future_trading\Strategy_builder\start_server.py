#!/usr/bin/env python3
"""
Simple server startup script for Strategy Builder
This script ensures all dependencies are working before starting the server
"""
import sys
import os
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def check_dependencies():
    """Check if all required dependencies are available"""
    try:
        # Test core imports
        from config.settings import settings
        from config.database import test_connection, health_check
        from backend.main import app
        
        logger.info("✅ Core dependencies imported successfully")
        return True
        
    except ImportError as e:
        logger.error(f"❌ Missing dependency: {e}")
        logger.info("💡 Try running: .venv\\Scripts\\python.exe -m pip install -r requirements.txt")
        return False
    except Exception as e:
        logger.error(f"❌ Dependency error: {e}")
        return False


def check_database():
    """Check database connection"""
    try:
        from config.database import test_connection, health_check
        
        if test_connection():
            health = health_check()
            logger.info(f"✅ Database connected: {health['database']} {health['version']}")
            return True
        else:
            logger.error("❌ Database connection failed")
            return False
            
    except Exception as e:
        logger.error(f"❌ Database error: {e}")
        return False


def check_api_keys():
    """Check API keys configuration"""
    try:
        from config.settings import settings
        
        binance_configured = bool(settings.binance.api_key)
        mexc_configured = bool(settings.mexc.api_key)
        
        logger.info(f"Binance API: {'✅ Configured' if binance_configured else '❌ Not configured'}")
        logger.info(f"MEXC API: {'✅ Configured' if mexc_configured else '❌ Not configured'}")
        
        if binance_configured or mexc_configured:
            logger.info("✅ At least one exchange API is configured")
            return True
        else:
            logger.warning("⚠️  No exchange APIs configured")
            return False
            
    except Exception as e:
        logger.error(f"❌ API keys check error: {e}")
        return False


def start_server():
    """Start the FastAPI server"""
    try:
        import uvicorn
        from backend.main import app
        
        logger.info("🚀 Starting Strategy Builder server...")
        logger.info("📍 Server will be available at: http://127.0.0.1:8000")
        logger.info("📖 API documentation: http://127.0.0.1:8000/docs")
        logger.info("🔄 Press Ctrl+C to stop the server")
        logger.info("")
        
        # Start the server
        uvicorn.run(
            app,
            host="127.0.0.1",
            port=8000,
            reload=False,
            log_level="info"
        )
        
    except KeyboardInterrupt:
        logger.info("\n👋 Server stopped by user")
    except Exception as e:
        logger.error(f"❌ Server error: {e}")
        return False
    
    return True


def main():
    """Main function"""
    logger.info("🔧 Strategy Builder - Starting Up")
    logger.info("=" * 50)
    
    # Run pre-flight checks
    checks = [
        ("Dependencies", check_dependencies),
        ("Database", check_database),
        ("API Keys", check_api_keys),
    ]
    
    all_passed = True
    for check_name, check_func in checks:
        logger.info(f"\n📋 Checking {check_name}...")
        if not check_func():
            all_passed = False
            logger.error(f"❌ {check_name} check failed")
        else:
            logger.info(f"✅ {check_name} check passed")
    
    if not all_passed:
        logger.error("\n❌ Pre-flight checks failed. Please fix the issues above.")
        return False
    
    logger.info("\n✅ All pre-flight checks passed!")
    logger.info("=" * 50)
    
    # Start the server
    return start_server()


if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except Exception as e:
        logger.error(f"❌ Startup error: {e}")
        sys.exit(1)
