#!/usr/bin/env python3
"""
Frontend Test Script for Strategy Builder
Tests the web interface functionality using Selenium
"""
import time
import logging
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.ui import Select
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Test Configuration
BASE_URL = "http://localhost:8000"
WAIT_TIMEOUT = 10


class FrontendTester:
    """Frontend functionality tester using Selenium"""
    
    def __init__(self, headless=False):
        self.driver = None
        self.headless = headless
        self.test_results = {}
        
    def setup_driver(self):
        """Setup Chrome WebDriver"""
        try:
            chrome_options = Options()
            if self.headless:
                chrome_options.add_argument("--headless")
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--window-size=1920,1080")
            
            self.driver = webdriver.Chrome(options=chrome_options)
            self.driver.implicitly_wait(5)
            logger.info("✅ Chrome WebDriver initialized")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize WebDriver: {e}")
            logger.info("Make sure Chrome and ChromeDriver are installed")
            return False
    
    def teardown_driver(self):
        """Close WebDriver"""
        if self.driver:
            self.driver.quit()
            logger.info("WebDriver closed")
    
    def wait_for_element(self, by, value, timeout=WAIT_TIMEOUT):
        """Wait for element to be present"""
        try:
            element = WebDriverWait(self.driver, timeout).until(
                EC.presence_of_element_located((by, value))
            )
            return element
        except TimeoutException:
            logger.warning(f"Element not found: {value}")
            return None
    
    def wait_for_clickable(self, by, value, timeout=WAIT_TIMEOUT):
        """Wait for element to be clickable"""
        try:
            element = WebDriverWait(self.driver, timeout).until(
                EC.element_to_be_clickable((by, value))
            )
            return element
        except TimeoutException:
            logger.warning(f"Element not clickable: {value}")
            return None
    
    def test_page_load(self):
        """Test initial page load"""
        try:
            logger.info("Testing page load...")
            self.driver.get(BASE_URL)
            
            # Wait for main elements
            title = self.wait_for_element(By.TAG_NAME, "h1")
            if title and "Strategy Builder" in title.text:
                logger.info("✅ Page loaded successfully")
                return True
            else:
                logger.error("❌ Page title not found or incorrect")
                return False
                
        except Exception as e:
            logger.error(f"❌ Page load failed: {e}")
            return False
    
    def test_ui_elements(self):
        """Test presence of main UI elements"""
        try:
            logger.info("Testing UI elements...")
            
            elements_to_check = [
                ("symbol-input", "Symbol input"),
                ("timeframe-select", "Timeframe selector"),
                ("exchange-select", "Exchange selector"),
                ("load-data-btn", "Load data button"),
                ("chart", "Chart container"),
                ("new-session-btn", "New session button"),
                ("calculate-indicators-btn", "Calculate indicators button"),
                ("mark-entry-buy-btn", "Mark entry buy button"),
                ("mark-entry-sell-btn", "Mark entry sell button"),
                ("mark-exit-btn", "Mark exit button")
            ]
            
            missing_elements = []
            for element_id, description in elements_to_check:
                element = self.driver.find_element(By.ID, element_id)
                if element:
                    logger.info(f"✅ {description} found")
                else:
                    logger.error(f"❌ {description} missing")
                    missing_elements.append(description)
            
            return len(missing_elements) == 0
            
        except Exception as e:
            logger.error(f"❌ UI elements test failed: {e}")
            return False
    
    def test_session_creation(self):
        """Test session creation functionality"""
        try:
            logger.info("Testing session creation...")
            
            # Click new session button
            new_session_btn = self.wait_for_clickable(By.ID, "new-session-btn")
            if not new_session_btn:
                return False
            
            new_session_btn.click()
            
            # Wait for modal
            modal = self.wait_for_element(By.ID, "new-session-modal")
            if not modal:
                logger.error("❌ Session modal not opened")
                return False
            
            # Fill session form
            session_name = self.driver.find_element(By.ID, "session-name-input")
            session_name.clear()
            session_name.send_keys("Frontend Test Session")
            
            # Click create button
            create_btn = self.wait_for_clickable(By.ID, "create-session-btn")
            if create_btn:
                create_btn.click()
                
                # Wait for modal to close
                time.sleep(2)
                
                # Check if session name updated
                session_display = self.driver.find_element(By.ID, "session-name")
                if "Frontend Test Session" in session_display.text:
                    logger.info("✅ Session created successfully")
                    return True
                else:
                    logger.error("❌ Session name not updated")
                    return False
            
            return False
            
        except Exception as e:
            logger.error(f"❌ Session creation test failed: {e}")
            return False
    
    def test_data_loading(self):
        """Test chart data loading"""
        try:
            logger.info("Testing data loading...")
            
            # Set symbol
            symbol_input = self.driver.find_element(By.ID, "symbol-input")
            symbol_input.clear()
            symbol_input.send_keys("BTCUSDT")
            
            # Set timeframe
            timeframe_select = Select(self.driver.find_element(By.ID, "timeframe-select"))
            timeframe_select.select_by_value("1h")
            
            # Set candles
            candles_input = self.driver.find_element(By.ID, "candles-input")
            candles_input.clear()
            candles_input.send_keys("100")
            
            # Click load data
            load_btn = self.wait_for_clickable(By.ID, "load-data-btn")
            if not load_btn:
                return False
            
            load_btn.click()
            
            # Wait for loading to complete (check for status change)
            time.sleep(5)
            
            # Check chart status
            chart_status = self.driver.find_element(By.ID, "chart-status")
            if chart_status and "Ready" in chart_status.text:
                logger.info("✅ Data loaded successfully")
                return True
            else:
                logger.warning("⚠️ Data loading status unclear")
                return True  # Don't fail for this
                
        except Exception as e:
            logger.error(f"❌ Data loading test failed: {e}")
            return False
    
    def test_indicators(self):
        """Test indicator functionality"""
        try:
            logger.info("Testing indicators...")
            
            # Check RSI checkbox
            rsi_checkbox = self.driver.find_element(By.ID, "rsi-checkbox")
            if not rsi_checkbox.is_selected():
                rsi_checkbox.click()
            
            # Check MACD checkbox
            macd_checkbox = self.driver.find_element(By.ID, "macd-checkbox")
            if not macd_checkbox.is_selected():
                macd_checkbox.click()
            
            # Click calculate indicators
            calc_btn = self.wait_for_clickable(By.ID, "calculate-indicators-btn")
            if not calc_btn:
                return False
            
            calc_btn.click()
            
            # Wait for calculation
            time.sleep(3)
            
            # Check if indicator panels are visible
            rsi_panel = self.driver.find_element(By.ID, "rsi-panel")
            macd_panel = self.driver.find_element(By.ID, "macd-panel")
            
            if rsi_panel.is_displayed() or macd_panel.is_displayed():
                logger.info("✅ Indicators calculated and displayed")
                return True
            else:
                logger.warning("⚠️ Indicator panels not visible")
                return True  # Don't fail for this
                
        except Exception as e:
            logger.error(f"❌ Indicators test failed: {e}")
            return False
    
    def test_trade_marking(self):
        """Test trade marking functionality"""
        try:
            logger.info("Testing trade marking...")
            
            # Check if chart is loaded by looking for chart container
            chart = self.driver.find_element(By.ID, "chart")
            if not chart:
                logger.error("❌ Chart not found for trade marking test")
                return False
            
            # Click on chart area (simulate chart click)
            chart.click()
            
            # Try to mark entry
            entry_buy_btn = self.wait_for_clickable(By.ID, "mark-entry-buy-btn")
            if entry_buy_btn and not entry_buy_btn.get_attribute("disabled"):
                entry_buy_btn.click()
                time.sleep(1)
                
                # Check if exit button is enabled
                exit_btn = self.driver.find_element(By.ID, "mark-exit-btn")
                if not exit_btn.get_attribute("disabled"):
                    logger.info("✅ Entry marking enabled exit button")
                    
                    # Try to mark exit
                    chart.click()  # Click chart again
                    exit_btn.click()
                    time.sleep(1)
                    
                    logger.info("✅ Trade marking functionality works")
                    return True
                else:
                    logger.warning("⚠️ Exit button not enabled after entry")
                    return True
            else:
                logger.warning("⚠️ Entry button not clickable")
                return True
                
        except Exception as e:
            logger.error(f"❌ Trade marking test failed: {e}")
            return False
    
    def test_export_functionality(self):
        """Test export functionality"""
        try:
            logger.info("Testing export functionality...")
            
            # Check export buttons
            json_btn = self.driver.find_element(By.ID, "export-json-btn")
            csv_btn = self.driver.find_element(By.ID, "export-csv-btn")
            
            if json_btn and csv_btn:
                logger.info("✅ Export buttons found")
                
                # Try clicking export buttons (they might not work without data)
                json_btn.click()
                time.sleep(1)
                
                csv_btn.click()
                time.sleep(1)
                
                logger.info("✅ Export buttons clickable")
                return True
            else:
                logger.error("❌ Export buttons not found")
                return False
                
        except Exception as e:
            logger.error(f"❌ Export test failed: {e}")
            return False
    
    def run_all_tests(self):
        """Run all frontend tests"""
        logger.info("🚀 Starting Frontend Tests")
        logger.info("=" * 50)
        
        if not self.setup_driver():
            return False
        
        try:
            tests = [
                ("Page Load", self.test_page_load),
                ("UI Elements", self.test_ui_elements),
                ("Session Creation", self.test_session_creation),
                ("Data Loading", self.test_data_loading),
                ("Indicators", self.test_indicators),
                ("Trade Marking", self.test_trade_marking),
                ("Export Functionality", self.test_export_functionality)
            ]
            
            passed = 0
            total = len(tests)
            
            for test_name, test_func in tests:
                logger.info(f"\n📋 Testing: {test_name}")
                logger.info("-" * 30)
                
                try:
                    result = test_func()
                    if result:
                        logger.info(f"✅ {test_name}: PASSED")
                        passed += 1
                    else:
                        logger.error(f"❌ {test_name}: FAILED")
                        
                    self.test_results[test_name] = result
                    
                except Exception as e:
                    logger.error(f"🔥 {test_name}: ERROR - {e}")
                    self.test_results[test_name] = False
            
            # Print summary
            logger.info("\n" + "=" * 50)
            logger.info("🏁 FRONTEND TEST SUMMARY")
            logger.info("=" * 50)
            logger.info(f"Total Tests: {total}")
            logger.info(f"✅ Passed: {passed}")
            logger.info(f"❌ Failed: {total - passed}")
            logger.info(f"Success Rate: {(passed/total)*100:.1f}%")
            
            if passed == total:
                logger.info("🎉 ALL FRONTEND TESTS PASSED!")
            elif passed > total * 0.7:
                logger.info("⚠️ Most tests passed. Minor issues detected.")
            else:
                logger.info("🚨 Multiple test failures. Review frontend implementation.")
            
            return passed == total
            
        finally:
            self.teardown_driver()


def main():
    """Main test function"""
    logger.info("Strategy Builder Frontend Tests")
    logger.info("Make sure the server is running: python run.py")
    logger.info("Chrome browser and ChromeDriver must be installed")
    logger.info("")
    
    # Ask for headless mode
    headless_input = input("Run in headless mode? (y/N): ").lower().strip()
    headless = headless_input == 'y'
    
    if not headless:
        input("Press Enter to start frontend tests (browser will open)...")
    
    tester = FrontendTester(headless=headless)
    success = tester.run_all_tests()
    
    if success:
        logger.info("\n🎯 Frontend is working correctly!")
    else:
        logger.info("\n🔧 Some frontend issues detected. Check the logs above.")


if __name__ == "__main__":
    main()
