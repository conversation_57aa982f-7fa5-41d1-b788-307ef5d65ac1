# Strategy Builder Setup Guide

## Quick Setup & Testing

### Prerequisites
- Python 3.8+
- MySQL 5.7+ or 8.0+
- Chrome browser (for frontend tests)

### 1. Database Setup

The database configuration is already set to:
```
Host: localhost
User: root
Password: @Oppa121089
Database: strategy_buider
```

**Create the database and tables:**
```bash
cd future_trading/Strategy_builder
python create_database.py
```

This will:
- Create the `strategy_buider` database
- Create all required tables
- Set up indexes and relationships
- Verify the setup

### 2. Install Dependencies

```bash
pip install -r requirements.txt
```

### 3. Run Comprehensive Tests

**Option A: Full Test Suite (Recommended)**
```bash
python run_tests.py
```

**Option B: Quick Tests (Skip frontend)**
```bash
python run_tests.py --quick
```

**Option C: Individual Test Components**
```bash
# Database and backend only
python tests/test_all_features.py

# API endpoints (requires server running)
python tests/test_api_endpoints.py

# Frontend (requires server running and Chrome)
python tests/test_frontend.py
```

### 4. Start the Application

```bash
python run.py
```

Then open: http://localhost:8000

## Test Components

### 1. Database Tests (`create_database.py`)
- Creates database and tables
- Verifies schema structure
- Tests connections
- Shows table information

### 2. Backend Tests (`test_all_features.py`)
- Database operations (OHLCV, indicators, marks, strategy logs)
- Technical indicator calculations
- Exchange API connections
- Session management
- Trade marking and P&L calculations
- Data export functionality
- Performance testing

### 3. API Tests (`test_api_endpoints.py`)
- Health and configuration endpoints
- OHLCV data endpoints
- Technical indicator endpoints
- Session management endpoints
- Trade marking endpoints
- Strategy analysis endpoints
- Export endpoints

### 4. Frontend Tests (`test_frontend.py`)
- Page loading and UI elements
- Session creation
- Chart data loading
- Indicator functionality
- Trade marking interface
- Export functionality

## Expected Test Results

### Database Setup
```
✅ Database 'strategy_buider' created successfully
✅ All tables created successfully
✅ Table 'ohlcv_data' exists
✅ Table 'indicators_data' exists
✅ Table 'manual_marks' exists
✅ Table 'strategy_log' exists
✅ Table 'strategy_sessions' exists
```

### Backend Tests
```
✅ Database Connection: PASSED
✅ Database Schema: PASSED
✅ Exchange APIs: PASSED
✅ OHLCV Operations: PASSED
✅ Technical Indicators: PASSED
✅ Session Management: PASSED
✅ Trade Marking: PASSED
✅ Strategy Logging: PASSED
✅ Data Export: PASSED
✅ Performance: PASSED
```

### API Tests
```
✅ Health Check: 200
✅ Configuration: 200
✅ OHLCV Data: 200
✅ Calculate Indicators: 200
✅ Create Session: 200
✅ Create Entry Mark: 200
✅ Create Exit Mark: 200
✅ Session Analytics: 200
```

### Frontend Tests
```
✅ Page Load: PASSED
✅ UI Elements: PASSED
✅ Session Creation: PASSED
✅ Data Loading: PASSED
✅ Indicators: PASSED
✅ Trade Marking: PASSED
✅ Export Functionality: PASSED
```

## Troubleshooting

### Database Issues
```bash
# Check MySQL service
sudo systemctl status mysql

# Test connection manually
mysql -u root -p@Oppa121089 -e "SHOW DATABASES;"

# Recreate database
python create_database.py
```

### Dependency Issues
```bash
# Reinstall dependencies
pip install --upgrade -r requirements.txt

# Check specific package
python -c "import fastapi; print('FastAPI OK')"
```

### Server Issues
```bash
# Check if port is in use
netstat -tulpn | grep :8000

# Kill existing process
pkill -f "python run.py"

# Start with debug
python run.py --host 0.0.0.0 --port 8001
```

### Frontend Issues
```bash
# Check Chrome installation
google-chrome --version

# Install ChromeDriver
# Download from: https://chromedriver.chromium.org/

# Run frontend tests in headless mode
python tests/test_frontend.py
# Choose 'y' for headless when prompted
```

## Performance Expectations

### Data Fetching
- 100 candles: < 2 seconds
- 1000 candles: < 5 seconds

### Indicator Calculation
- 3 indicators on 1000 candles: < 3 seconds
- RSI calculation: < 1 second
- MACD calculation: < 1 second

### Database Operations
- Insert 1000 OHLCV records: < 2 seconds
- Query 1000 records: < 1 second
- Create trade marks: < 0.5 seconds

## Success Criteria

The application is ready for use when:

1. **Database Setup**: ✅ All tables created successfully
2. **Backend Tests**: ✅ All 10 test suites pass
3. **API Tests**: ✅ All endpoints return 200 status
4. **Frontend Tests**: ✅ All UI components work correctly
5. **Performance**: ✅ Operations complete within expected timeframes

## Next Steps After Successful Testing

1. **Production Deployment**
   - Configure production database
   - Set up environment variables
   - Configure reverse proxy (nginx)
   - Set up SSL certificates

2. **Monitoring**
   - Set up application logging
   - Configure database monitoring
   - Set up health check endpoints

3. **User Training**
   - Create user documentation
   - Conduct training sessions
   - Set up support procedures

4. **Backup & Recovery**
   - Configure database backups
   - Test restore procedures
   - Document recovery processes

## Support

If tests fail or you encounter issues:

1. Check the generated `test_report.md` file
2. Review logs for specific error messages
3. Verify all prerequisites are installed
4. Check database connectivity
5. Ensure no other services are using port 8000

For additional help, review the main README.md file or check the API documentation at http://localhost:8000/docs when the server is running.
