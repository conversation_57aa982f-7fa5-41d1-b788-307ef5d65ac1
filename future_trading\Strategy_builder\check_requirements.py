#!/usr/bin/env python3
"""
Requirements Checker for Strategy Builder
Simple script to check if all requirements are met
"""
import sys
import subprocess
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def check_python_version():
    """Check Python version"""
    logger.info("Checking Python Version")
    logger.info("-" * 30)
    
    version = sys.version_info
    logger.info(f"Python version: {version.major}.{version.minor}.{version.micro}")
    
    if version.major >= 3 and version.minor >= 8:
        logger.info("Python version: OK")
        return True
    else:
        logger.error("Python version: FAILED (requires 3.8+)")
        return False


def check_packages():
    """Check required packages"""
    logger.info("\nChecking Required Packages")
    logger.info("-" * 30)
    
    required_packages = [
        "fastapi",
        "uvicorn", 
        "mysql-connector-python",
        "pandas",
        "pandas_ta",
        "requests",
        "sqlalchemy",
        "pydantic"
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            # Try to import the package
            if package == "mysql-connector-python":
                import mysql.connector
            elif package == "pandas_ta":
                import pandas_ta
            else:
                __import__(package)
            
            logger.info(f"{package}: OK")
        except ImportError:
            logger.error(f"{package}: MISSING")
            missing_packages.append(package)
    
    if missing_packages:
        logger.error(f"\nMissing packages: {', '.join(missing_packages)}")
        logger.info("Install with: pip install -r requirements.txt")
        return False
    else:
        logger.info("\nAll packages: OK")
        return True


def check_database_config():
    """Check database configuration"""
    logger.info("\nChecking Database Configuration")
    logger.info("-" * 30)
    
    try:
        import mysql.connector
        
        # Test connection with the configured credentials
        config = {
            'host': 'localhost',
            'user': 'root',
            'password': '@Oppa121089'
        }
        
        connection = mysql.connector.connect(**config)
        if connection.is_connected():
            cursor = connection.cursor()
            cursor.execute("SELECT VERSION()")
            version = cursor.fetchone()[0]
            logger.info(f"MySQL connection: OK")
            logger.info(f"MySQL version: {version}")
            
            # Check if database exists
            cursor.execute("SHOW DATABASES LIKE 'strategy_buider'")
            db_exists = cursor.fetchone() is not None
            
            if db_exists:
                logger.info("Database 'strategy_buider': EXISTS")
            else:
                logger.warning("Database 'strategy_buider': NOT FOUND")
                logger.info("Run: python create_database.py")
            
            cursor.close()
            connection.close()
            return True
        else:
            logger.error("MySQL connection: FAILED")
            return False
            
    except Exception as e:
        logger.error(f"Database check failed: {e}")
        logger.info("Make sure MySQL is running and credentials are correct")
        return False


def check_file_structure():
    """Check if required files exist"""
    logger.info("\nChecking File Structure")
    logger.info("-" * 30)
    
    required_files = [
        "requirements.txt",
        "create_database.py",
        "run.py",
        "config/settings.py",
        "config/database.py",
        "backend/main.py",
        "database/schema.sql",
        "frontend/index.html"
    ]
    
    missing_files = []
    
    import os
    for file_path in required_files:
        if os.path.exists(file_path):
            logger.info(f"{file_path}: OK")
        else:
            logger.error(f"{file_path}: MISSING")
            missing_files.append(file_path)
    
    if missing_files:
        logger.error(f"\nMissing files: {', '.join(missing_files)}")
        return False
    else:
        logger.info("\nAll required files: OK")
        return True


def main():
    """Main function"""
    logger.info("Strategy Builder Requirements Check")
    logger.info("=" * 50)
    
    checks = [
        ("Python Version", check_python_version),
        ("Required Packages", check_packages),
        ("File Structure", check_file_structure),
        ("Database Configuration", check_database_config)
    ]
    
    results = {}
    
    for check_name, check_func in checks:
        try:
            result = check_func()
            results[check_name] = result
        except Exception as e:
            logger.error(f"{check_name} check failed: {e}")
            results[check_name] = False
    
    # Print summary
    logger.info("\n" + "=" * 50)
    logger.info("REQUIREMENTS CHECK SUMMARY")
    logger.info("=" * 50)
    
    total = len(results)
    passed = sum(1 for result in results.values() if result)
    
    logger.info(f"Total Checks: {total}")
    logger.info(f"Passed: {passed}")
    logger.info(f"Failed: {total - passed}")
    
    for check_name, result in results.items():
        status = "PASSED" if result else "FAILED"
        logger.info(f"  {check_name}: {status}")
    
    if passed == total:
        logger.info("\nALL REQUIREMENTS MET!")
        logger.info("You can now run the Strategy Builder:")
        logger.info("1. python test_windows.py  (run tests)")
        logger.info("2. python run.py           (start application)")
        logger.info("3. Open: http://localhost:8000")
    else:
        logger.info(f"\n{total - passed} REQUIREMENT(S) NOT MET!")
        logger.info("Please fix the issues above before proceeding.")
        
        if not results.get("Required Packages", True):
            logger.info("\nTo install packages:")
            logger.info("pip install -r requirements.txt")
        
        if not results.get("Database Configuration", True):
            logger.info("\nTo setup database:")
            logger.info("python create_database.py")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
