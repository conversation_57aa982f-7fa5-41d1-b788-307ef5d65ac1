#!/usr/bin/env python3
"""
Database setup script for Strategy Builder
"""
import sys
import os
import mysql.connector
from mysql.connector import Error
import logging

# Add parent directory to path to import config
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.settings import settings
from config.database import create_tables, test_connection, health_check

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def create_database():
    """Create the strategy_builder database if it doesn't exist"""
    try:
        # Connect to MySQL server (without specifying database)
        connection = mysql.connector.connect(
            host='localhost',
            port=3306,
            user='root',
            password='@Oppa121089'
        )
        
        if connection.is_connected():
            cursor = connection.cursor()
            
            # Create database
            cursor.execute("CREATE DATABASE IF NOT EXISTS strategy_buider")
            logger.info("Database 'strategy_buider' created or already exists")

            # Grant privileges (optional, for development)
            try:
                cursor.execute("GRANT ALL PRIVILEGES ON strategy_buider.* TO 'root'@'%'")
                cursor.execute("FLUSH PRIVILEGES")
                logger.info("Privileges granted")
            except Error as e:
                logger.warning(f"Could not grant privileges: {e}")
            
            cursor.close()
            connection.close()
            return True
            
    except Error as e:
        logger.error(f"Error creating database: {e}")
        return False


def execute_sql_file(file_path: str):
    """Execute SQL file"""
    try:
        connection = mysql.connector.connect(
            host='localhost',
            port=3306,
            user='root',
            password='@Oppa121089',
            database='strategy_buider'
        )
        
        if connection.is_connected():
            cursor = connection.cursor()
            
            # Read and execute SQL file
            with open(file_path, 'r') as file:
                sql_script = file.read()
                
            # Split by semicolon and execute each statement
            statements = sql_script.split(';')
            for statement in statements:
                statement = statement.strip()
                if statement:
                    cursor.execute(statement)
            
            connection.commit()
            logger.info(f"SQL file '{file_path}' executed successfully")
            
            cursor.close()
            connection.close()
            return True
            
    except Error as e:
        logger.error(f"Error executing SQL file: {e}")
        return False
    except FileNotFoundError:
        logger.error(f"SQL file not found: {file_path}")
        return False


def verify_tables():
    """Verify that all tables were created"""
    try:
        connection = mysql.connector.connect(
            host='localhost',
            port=3306,
            user='root',
            password='@Oppa121089',
            database='strategy_buider'
        )
        
        if connection.is_connected():
            cursor = connection.cursor()
            
            # Get list of tables
            cursor.execute("SHOW TABLES")
            tables = [table[0] for table in cursor.fetchall()]
            
            expected_tables = [
                'ohlcv_data',
                'indicators_data', 
                'manual_marks',
                'strategy_log',
                'strategy_sessions'
            ]
            
            logger.info(f"Found tables: {tables}")
            
            missing_tables = [table for table in expected_tables if table not in tables]
            if missing_tables:
                logger.error(f"Missing tables: {missing_tables}")
                return False
            else:
                logger.info("✅ All expected tables found")
                return True
            
    except Error as e:
        logger.error(f"Error verifying tables: {e}")
        return False


def setup_database():
    """Main database setup function"""
    logger.info("🚀 Starting database setup...")
    
    # Step 1: Create database
    logger.info("Step 1: Creating database...")
    if not create_database():
        logger.error("❌ Failed to create database")
        return False
    
    # Step 2: Execute schema SQL file
    logger.info("Step 2: Creating tables from schema...")
    schema_file = os.path.join(os.path.dirname(__file__), 'schema.sql')
    if not execute_sql_file(schema_file):
        logger.error("❌ Failed to create tables from schema")
        return False
    
    # Step 3: Verify tables
    logger.info("Step 3: Verifying tables...")
    if not verify_tables():
        logger.error("❌ Table verification failed")
        return False
    
    # Step 4: Test connection using SQLAlchemy
    logger.info("Step 4: Testing SQLAlchemy connection...")
    if not test_connection():
        logger.error("❌ SQLAlchemy connection test failed")
        return False
    
    # Step 5: Health check
    logger.info("Step 5: Performing health check...")
    health = health_check()
    if health['status'] == 'healthy':
        logger.info(f"✅ Database health check passed: {health}")
    else:
        logger.error(f"❌ Database health check failed: {health}")
        return False
    
    logger.info("🎉 Database setup completed successfully!")
    return True


def reset_database():
    """Reset database (drop and recreate)"""
    logger.warning("⚠️  Resetting database - all data will be lost!")
    
    try:
        connection = mysql.connector.connect(
            host='localhost',
            port=3306,
            user='root',
            password='@Oppa121089'
        )
        
        if connection.is_connected():
            cursor = connection.cursor()
            
            # Drop database
            cursor.execute("DROP DATABASE IF EXISTS strategy_buider")
            logger.info("Database 'strategy_buider' dropped")
            
            cursor.close()
            connection.close()
            
            # Recreate database
            return setup_database()
            
    except Error as e:
        logger.error(f"Error resetting database: {e}")
        return False


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='Strategy Builder Database Setup')
    parser.add_argument('--reset', action='store_true', help='Reset database (drop and recreate)')
    parser.add_argument('--verify', action='store_true', help='Only verify existing setup')
    
    args = parser.parse_args()
    
    if args.reset:
        success = reset_database()
    elif args.verify:
        success = verify_tables() and test_connection()
    else:
        success = setup_database()
    
    sys.exit(0 if success else 1)
