#!/usr/bin/env python3
"""
Strategy Builder Startup Script
"""
import os
import sys
import subprocess
import argparse
import logging

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config.settings import settings
from config.database import test_connection, health_check

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def check_dependencies():
    """Check if all required dependencies are installed"""
    try:
        import fastapi
        import uvicorn
        import mysql.connector
        import pandas
        import pandas_ta
        logger.info("✅ All dependencies are installed")
        return True
    except ImportError as e:
        logger.error(f"❌ Missing dependency: {e}")
        logger.info("Please run: pip install -r requirements.txt")
        return False


def check_database():
    """Check database connection and setup"""
    try:
        if test_connection():
            logger.info("✅ Database connection successful")
            
            health = health_check()
            if health['status'] == 'healthy':
                logger.info(f"✅ Database health check passed: {health}")
                return True
            else:
                logger.error(f"❌ Database health check failed: {health}")
                return False
        else:
            logger.error("❌ Database connection failed")
            logger.info("Please run: python database/setup_database.py")
            return False
    except Exception as e:
        logger.error(f"❌ Database check failed: {e}")
        return False


def setup_database():
    """Setup database"""
    try:
        logger.info("Setting up database...")
        result = subprocess.run([
            sys.executable, 
            "database/setup_database.py"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            logger.info("✅ Database setup completed")
            return True
        else:
            logger.error(f"❌ Database setup failed: {result.stderr}")
            return False
    except Exception as e:
        logger.error(f"❌ Database setup error: {e}")
        return False


def install_dependencies():
    """Install dependencies"""
    try:
        logger.info("Installing dependencies...")
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            logger.info("✅ Dependencies installed")
            return True
        else:
            logger.error(f"❌ Dependency installation failed: {result.stderr}")
            return False
    except Exception as e:
        logger.error(f"❌ Dependency installation error: {e}")
        return False


def run_server(host=None, port=None, reload=None):
    """Run the FastAPI server"""
    try:
        host = host or settings.app.host
        port = port or settings.app.port
        reload = reload if reload is not None else settings.app.debug
        
        logger.info(f"🚀 Starting Strategy Builder server...")
        logger.info(f"📍 Server will be available at: http://{host}:{port}")
        logger.info(f"📖 API documentation: http://{host}:{port}/docs")
        logger.info(f"🔄 Auto-reload: {'enabled' if reload else 'disabled'}")
        
        # Import and run
        import uvicorn
        uvicorn.run(
            "backend.main:app",
            host=host,
            port=port,
            reload=reload,
            log_level="info"
        )
        
    except KeyboardInterrupt:
        logger.info("🛑 Server stopped by user")
    except Exception as e:
        logger.error(f"❌ Server error: {e}")


def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='Strategy Builder Startup Script')
    parser.add_argument('--host', default=None, help='Host to bind to')
    parser.add_argument('--port', type=int, default=None, help='Port to bind to')
    parser.add_argument('--no-reload', action='store_true', help='Disable auto-reload')
    parser.add_argument('--setup-db', action='store_true', help='Setup database only')
    parser.add_argument('--install-deps', action='store_true', help='Install dependencies only')
    parser.add_argument('--check-only', action='store_true', help='Check setup only')
    
    args = parser.parse_args()
    
    logger.info("🔧 Strategy Builder - Phase 1 MVP")
    logger.info("=" * 50)
    
    # Install dependencies if requested
    if args.install_deps:
        if install_dependencies():
            logger.info("✅ Dependencies installation completed")
        else:
            logger.error("❌ Dependencies installation failed")
        return
    
    # Setup database if requested
    if args.setup_db:
        if setup_database():
            logger.info("✅ Database setup completed")
        else:
            logger.error("❌ Database setup failed")
        return
    
    # Check dependencies
    if not check_dependencies():
        logger.info("Run with --install-deps to install dependencies")
        return
    
    # Check database
    if not check_database():
        logger.info("Run with --setup-db to setup database")
        return
    
    # If check-only, exit here
    if args.check_only:
        logger.info("✅ All checks passed - ready to run!")
        return
    
    # Run server
    reload = not args.no_reload
    run_server(args.host, args.port, reload)


if __name__ == "__main__":
    main()
