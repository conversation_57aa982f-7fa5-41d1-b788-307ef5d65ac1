"""
Database operations for Strategy Builder
Now using PyMySQL for direct database operations
"""
from typing import List, Dict, Any, Optional
from datetime import datetime
import uuid
import json
import logging

from config.pymysql_db import (
    OHLCVOperations as BaseOHLCVOps,
    IndicatorsOperations as BaseIndicatorsOps, 
    ManualMarksOperations as BaseManualMarksOps,
    StrategySessionsOperations as BaseStrategySessionsOps
)

logger = logging.getLogger(__name__)


class OHLCVOperations(BaseOHLCVOps):
    """Extended OHLCV operations"""
    
    @staticmethod
    def store_ohlcv_data(symbol: str, timeframe: str, ohlcv_data: List[Dict[str, Any]]) -> bool:
        """Store OHLCV data with proper formatting"""
        try:
            # Format data for database insertion
            formatted_data = []
            for candle in ohlcv_data:
                formatted_data.append({
                    'timestamp': datetime.fromtimestamp(candle['timestamp'] / 1000) if isinstance(candle['timestamp'], (int, float)) else candle['timestamp'],
                    'open': float(candle['open']),
                    'high': float(candle['high']),
                    'low': float(candle['low']),
                    'close': float(candle['close']),
                    'volume': float(candle['volume'])
                })
            
            BaseOHLCVOps.insert_ohlcv_data(symbol, timeframe, formatted_data)
            logger.info(f"Stored {len(formatted_data)} OHLCV records for {symbol} {timeframe}")
            return True
            
        except Exception as e:
            logger.error(f"Error storing OHLCV data: {e}")
            return False
    
    @staticmethod
    def get_ohlcv_for_chart(symbol: str, timeframe: str, limit: int = 500) -> List[Dict[str, Any]]:
        """Get OHLCV data formatted for chart display"""
        try:
            data = BaseOHLCVOps.get_ohlcv_data(symbol, timeframe, limit)
            
            # Format for chart (reverse order for chronological display)
            chart_data = []
            for row in reversed(data):
                chart_data.append({
                    'timestamp': int(row['timestamp'].timestamp() * 1000),
                    'open': float(row['open']),
                    'high': float(row['high']),
                    'low': float(row['low']),
                    'close': float(row['close']),
                    'volume': float(row['volume'])
                })
            
            return chart_data
            
        except Exception as e:
            logger.error(f"Error getting OHLCV data for chart: {e}")
            return []


class IndicatorsOperations(BaseIndicatorsOps):
    """Extended indicators operations"""
    
    @staticmethod
    def store_indicators_data(symbol: str, timeframe: str, indicators_data: Dict[str, Any]) -> bool:
        """Store calculated indicators data"""
        try:
            formatted_data = []
            
            # Process each indicator
            for indicator_name, indicator_values in indicators_data.items():
                if not indicator_values:
                    continue
                    
                # Handle different indicator data structures
                if isinstance(indicator_values, dict) and 'values' in indicator_values:
                    values = indicator_values['values']
                    params = indicator_values.get('params', {})
                else:
                    values = indicator_values
                    params = {}
                
                # Create records for each timestamp
                for i, value in enumerate(values):
                    if value is not None:
                        formatted_data.append({
                            'timestamp': datetime.fromtimestamp(value.get('timestamp', 0) / 1000) if isinstance(value, dict) else datetime.now(),
                            'indicator_name': indicator_name,
                            'params': params,
                            'value': value
                        })
            
            if formatted_data:
                BaseIndicatorsOps.insert_indicators_data(symbol, timeframe, formatted_data)
                logger.info(f"Stored {len(formatted_data)} indicator records for {symbol} {timeframe}")
            
            return True
            
        except Exception as e:
            logger.error(f"Error storing indicators data: {e}")
            return False
    
    @staticmethod
    def get_indicators_for_chart(symbol: str, timeframe: str, limit: int = 500) -> Dict[str, Any]:
        """Get indicators data formatted for chart display"""
        try:
            data = BaseIndicatorsOps.get_indicators_data(symbol, timeframe, None, limit)
            
            # Group by indicator name
            indicators = {}
            for row in data:
                indicator_name = row['indicator_name']
                if indicator_name not in indicators:
                    indicators[indicator_name] = {
                        'name': indicator_name,
                        'params': row.get('indicator_params', {}),
                        'values': []
                    }
                
                indicators[indicator_name]['values'].append({
                    'timestamp': int(row['timestamp'].timestamp() * 1000),
                    'value': row['value']
                })
            
            # Sort values by timestamp for each indicator
            for indicator in indicators.values():
                indicator['values'].sort(key=lambda x: x['timestamp'])
            
            return indicators
            
        except Exception as e:
            logger.error(f"Error getting indicators data for chart: {e}")
            return {}


class ManualMarksOperations(BaseManualMarksOps):
    """Extended manual marks operations"""
    
    @staticmethod
    def create_manual_mark(symbol: str, timeframe: str, mark_data: Dict[str, Any]) -> str:
        """Create a new manual mark"""
        try:
            mark_id = str(uuid.uuid4())
            
            formatted_mark = {
                'id': mark_id,
                'symbol': symbol,
                'timeframe': timeframe,
                'timestamp': datetime.fromtimestamp(mark_data['timestamp'] / 1000) if isinstance(mark_data['timestamp'], (int, float)) else mark_data['timestamp'],
                'price': float(mark_data['price']),
                'mark_type': mark_data['mark_type'],
                'entry_side': mark_data.get('entry_side'),
                'linked_trade_id': mark_data.get('linked_trade_id'),
                'indicator_snapshot': mark_data.get('indicator_snapshot', {}),
                'ohlcv_snapshot': mark_data.get('ohlcv_snapshot', {}),
                'session_id': mark_data.get('session_id')
            }
            
            BaseManualMarksOps.insert_manual_mark(formatted_mark)
            logger.info(f"Created manual mark {mark_id} for {symbol} {timeframe}")
            return mark_id
            
        except Exception as e:
            logger.error(f"Error creating manual mark: {e}")
            raise
    
    @staticmethod
    def get_marks_for_chart(symbol: str, timeframe: str, session_id: str = None) -> List[Dict[str, Any]]:
        """Get manual marks formatted for chart display"""
        try:
            data = BaseManualMarksOps.get_manual_marks(symbol, timeframe, session_id)
            
            # Format for chart
            marks = []
            for row in data:
                marks.append({
                    'id': row['id'],
                    'timestamp': int(row['timestamp'].timestamp() * 1000),
                    'price': float(row['price']),
                    'mark_type': row['mark_type'],
                    'entry_side': row['entry_side'],
                    'linked_trade_id': row['linked_trade_id'],
                    'indicator_snapshot': row.get('indicator_snapshot', {}),
                    'ohlcv_snapshot': row.get('ohlcv_snapshot', {})
                })
            
            return marks
            
        except Exception as e:
            logger.error(f"Error getting marks for chart: {e}")
            return []


class StrategySessionsOperations(BaseStrategySessionsOps):
    """Extended strategy sessions operations"""
    
    @staticmethod
    def create_new_session(symbol: str, timeframe: str, session_name: str = None) -> str:
        """Create a new strategy session"""
        try:
            session_id = str(uuid.uuid4())
            
            session_data = {
                'session_id': session_id,
                'session_name': session_name or f"{symbol}_{timeframe}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                'symbol': symbol,
                'timeframe': timeframe
            }
            
            BaseStrategySessionsOps.create_session(session_data)
            logger.info(f"Created new session {session_id} for {symbol} {timeframe}")
            return session_id
            
        except Exception as e:
            logger.error(f"Error creating session: {e}")
            raise
    
    @staticmethod
    def get_active_sessions(symbol: str = None, timeframe: str = None) -> List[Dict[str, Any]]:
        """Get active strategy sessions"""
        try:
            data = BaseStrategySessionsOps.get_sessions(symbol, timeframe)
            
            # Format session data
            sessions = []
            for row in data:
                sessions.append({
                    'session_id': row['session_id'],
                    'session_name': row['session_name'],
                    'symbol': row['symbol'],
                    'timeframe': row['timeframe'],
                    'total_trades': row.get('total_trades', 0),
                    'winning_trades': row.get('winning_trades', 0),
                    'losing_trades': row.get('losing_trades', 0),
                    'win_rate': float(row.get('win_rate', 0)),
                    'total_profit_pct': float(row.get('total_profit_pct', 0)),
                    'created_at': row['created_at'].isoformat() if row.get('created_at') else None
                })
            
            return sessions
            
        except Exception as e:
            logger.error(f"Error getting active sessions: {e}")
            return []


# Export the operations classes
__all__ = [
    'OHLCVOperations',
    'IndicatorsOperations', 
    'ManualMarksOperations',
    'StrategySessionsOperations'
]
