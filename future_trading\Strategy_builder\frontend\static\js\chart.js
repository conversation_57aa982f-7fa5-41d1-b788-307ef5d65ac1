/**
 * Chart Management for Strategy Builder
 */

class ChartManager {
    constructor() {
        this.chart = null;
        this.candlestickSeries = null;
        this.volumeSeries = null;
        this.indicatorSeries = {};
        this.indicatorCharts = {};
        this.markers = [];
        this.currentData = null;
        this.currentSymbol = CONFIG.DEFAULT_SYMBOL;
        this.currentTimeframe = CONFIG.DEFAULT_TIMEFRAME;
        
        this.initializeChart();
        this.setupEventListeners();
    }

    /**
     * Initialize the main chart
     */
    initializeChart() {
        const chartContainer = document.getElementById('chart');
        if (!chartContainer) {
            console.error('Chart container not found');
            return;
        }

        // Create chart
        this.chart = LightweightCharts.createChart(chartContainer, {
            ...CONFIG.CHART_OPTIONS,
            width: chartContainer.clientWidth,
            height: CONFIG.CHART_HEIGHT
        });

        // Create candlestick series
        try {
            if (typeof this.chart.addCandlestickSeries === 'function') {
                this.candlestickSeries = this.chart.addCandlestickSeries({
                    upColor: '#26a69a',
                    downColor: '#ef5350',
                    borderVisible: false,
                    wickUpColor: '#26a69a',
                    wickDownColor: '#ef5350'
                });
            } else {
                console.error('addCandlestickSeries method not found. Chart library may not be loaded properly.');
                // Fallback: create a simple line series
                this.candlestickSeries = this.chart.addLineSeries({
                    color: '#26a69a',
                    lineWidth: 2
                });
            }
        } catch (error) {
            console.error('Error creating candlestick series:', error);
            // Fallback: create a simple line series
            this.candlestickSeries = this.chart.addLineSeries({
                color: '#26a69a',
                lineWidth: 2
            });
        }

        // Create volume series
        this.volumeSeries = this.chart.addHistogramSeries({
            color: '#26a69a',
            priceFormat: {
                type: 'volume',
            },
            priceScaleId: 'volume',
            scaleMargins: {
                top: 0.8,
                bottom: 0,
            },
        });

        // Setup chart interactions
        this.setupChartInteractions();
    }

    /**
     * Setup chart interactions
     */
    setupChartInteractions() {
        // Right-click context menu for marking trades
        this.chart.subscribeClick((param) => {
            if (param.time && param.point) {
                this.handleChartClick(param);
            }
        });

        // Crosshair move for real-time data display
        this.chart.subscribeCrosshairMove((param) => {
            this.handleCrosshairMove(param);
        });
    }

    /**
     * Handle chart click for trade marking
     */
    handleChartClick(param) {
        const time = param.time;
        const price = param.seriesPrices.get(this.candlestickSeries);
        
        if (time && price) {
            // Store click data for trade marking
            this.lastClickData = {
                time: time,
                price: price,
                timestamp: new Date(time * 1000).toISOString()
            };
            
            // Show context menu or trigger marking based on current mode
            this.showTradeMarkingOptions(param.point.x, param.point.y);
        }
    }

    /**
     * Show trade marking options
     */
    showTradeMarkingOptions(x, y) {
        // For now, just log the click - this would show a context menu
        console.log('Chart clicked at:', this.lastClickData);
        
        // Enable marking buttons
        const markEntryBuyBtn = document.getElementById('mark-entry-buy-btn');
        const markEntrySellBtn = document.getElementById('mark-entry-sell-btn');
        
        if (markEntryBuyBtn) markEntryBuyBtn.disabled = false;
        if (markEntrySellBtn) markEntrySellBtn.disabled = false;
    }

    /**
     * Handle crosshair move
     */
    handleCrosshairMove(param) {
        if (param.time) {
            const data = param.seriesPrices.get(this.candlestickSeries);
            if (data) {
                // Update price display or other real-time info
                this.updatePriceDisplay(data);
            }
        }
    }

    /**
     * Update price display
     */
    updatePriceDisplay(priceData) {
        // Update chart title or status with current price
        const chartStatus = document.getElementById('chart-status');
        if (chartStatus && priceData) {
            chartStatus.textContent = `O: ${UTILS.formatNumber(priceData.open, 4)} H: ${UTILS.formatNumber(priceData.high, 4)} L: ${UTILS.formatNumber(priceData.low, 4)} C: ${UTILS.formatNumber(priceData.close, 4)}`;
        }
    }

    /**
     * Load OHLCV data
     */
    async loadData(exchange, symbol, timeframe, options = {}) {
        try {
            UTILS.showLoading('Loading chart data...');
            
            const response = await api.getOHLCVData(exchange, symbol, timeframe, options);
            
            if (!response.data || response.data.length === 0) {
                throw new Error('No data received');
            }

            this.currentData = response.data;
            this.currentSymbol = symbol;
            this.currentTimeframe = timeframe;

            // Convert data for chart
            const candleData = this.convertOHLCVData(response.data);
            const volumeData = this.convertVolumeData(response.data);

            // Update chart
            this.candlestickSeries.setData(candleData);
            this.volumeSeries.setData(volumeData);

            // Update chart title
            this.updateChartTitle(symbol, timeframe);

            // Fit content
            this.chart.timeScale().fitContent();

            UTILS.hideLoading();
            UTILS.showNotification(CONFIG.SUCCESS_MESSAGES.DATA_LOADED, 'success');

            return response;
        } catch (error) {
            UTILS.hideLoading();
            UTILS.showNotification(`Error loading data: ${error.message}`, 'error');
            throw error;
        }
    }

    /**
     * Convert OHLCV data for chart
     */
    convertOHLCVData(data) {
        return data.map(candle => ({
            time: Math.floor(new Date(candle.timestamp).getTime() / 1000),
            open: candle.open,
            high: candle.high,
            low: candle.low,
            close: candle.close
        }));
    }

    /**
     * Convert volume data for chart
     */
    convertVolumeData(data) {
        return data.map(candle => ({
            time: Math.floor(new Date(candle.timestamp).getTime() / 1000),
            value: candle.volume,
            color: candle.close >= candle.open ? '#26a69a' : '#ef5350'
        }));
    }

    /**
     * Update chart title
     */
    updateChartTitle(symbol, timeframe) {
        const symbolElement = document.getElementById('chart-symbol');
        const timeframeElement = document.getElementById('chart-timeframe');
        
        if (symbolElement) symbolElement.textContent = symbol;
        if (timeframeElement) timeframeElement.textContent = timeframe;
    }

    /**
     * Add indicator overlay (EMA, SMA, etc.)
     */
    addIndicatorOverlay(indicatorName, data, config = {}) {
        const color = CONFIG.INDICATOR_COLORS[indicatorName] || '#ffffff';
        
        const series = this.chart.addLineSeries({
            color: color,
            lineWidth: 2,
            title: indicatorName,
            ...config
        });

        const chartData = this.convertIndicatorData(data, indicatorName);
        series.setData(chartData);

        this.indicatorSeries[indicatorName] = series;
        return series;
    }

    /**
     * Add indicator panel (RSI, MACD, etc.)
     */
    addIndicatorPanel(indicatorName, data, config = {}) {
        const panelId = `${indicatorName.toLowerCase()}-panel`;
        const chartId = `${indicatorName.toLowerCase()}-chart`;
        
        const panel = document.getElementById(panelId);
        const chartContainer = document.getElementById(chartId);
        
        if (!panel || !chartContainer) {
            console.error(`Indicator panel not found: ${panelId}`);
            return;
        }

        // Show panel
        panel.style.display = 'block';

        // Create indicator chart
        const indicatorChart = LightweightCharts.createChart(chartContainer, {
            ...CONFIG.CHART_OPTIONS,
            width: chartContainer.clientWidth,
            height: CONFIG.INDICATOR_PANEL_HEIGHT
        });

        // Add indicator-specific series
        this.addIndicatorSeries(indicatorChart, indicatorName, data, config);

        this.indicatorCharts[indicatorName] = indicatorChart;
        return indicatorChart;
    }

    /**
     * Add indicator series based on type
     */
    addIndicatorSeries(chart, indicatorName, data, config) {
        const colors = CONFIG.INDICATOR_COLORS[indicatorName];
        
        switch (indicatorName) {
            case 'RSI':
                const rsiSeries = chart.addLineSeries({
                    color: colors,
                    lineWidth: 2
                });
                
                // Add RSI levels
                chart.addLineSeries({
                    color: '#666666',
                    lineWidth: 1,
                    lineStyle: LightweightCharts.LineStyle.Dashed
                }).setData([{ time: 0, value: 70 }, { time: Date.now() / 1000, value: 70 }]);
                
                chart.addLineSeries({
                    color: '#666666',
                    lineWidth: 1,
                    lineStyle: LightweightCharts.LineStyle.Dashed
                }).setData([{ time: 0, value: 30 }, { time: Date.now() / 1000, value: 30 }]);
                
                const rsiData = this.convertIndicatorData(data, 'RSI');
                rsiSeries.setData(rsiData);
                break;
                
            case 'MACD':
                const macdSeries = chart.addLineSeries({
                    color: colors.macd,
                    lineWidth: 2
                });
                
                const signalSeries = chart.addLineSeries({
                    color: colors.signal,
                    lineWidth: 2
                });
                
                const histogramSeries = chart.addHistogramSeries({
                    color: colors.histogram
                });
                
                const macdData = this.convertIndicatorData(data, 'MACD');
                macdSeries.setData(macdData.macd);
                signalSeries.setData(macdData.signal);
                histogramSeries.setData(macdData.histogram);
                break;
        }
    }

    /**
     * Convert indicator data for chart
     */
    convertIndicatorData(data, indicatorName) {
        switch (indicatorName) {
            case 'RSI':
                return data.map(item => ({
                    time: Math.floor(new Date(item.timestamp).getTime() / 1000),
                    value: item.value.rsi
                })).filter(item => item.value !== null);
                
            case 'MACD':
                return {
                    macd: data.map(item => ({
                        time: Math.floor(new Date(item.timestamp).getTime() / 1000),
                        value: item.value.macd
                    })).filter(item => item.value !== null),
                    
                    signal: data.map(item => ({
                        time: Math.floor(new Date(item.timestamp).getTime() / 1000),
                        value: item.value.signal
                    })).filter(item => item.value !== null),
                    
                    histogram: data.map(item => ({
                        time: Math.floor(new Date(item.timestamp).getTime() / 1000),
                        value: item.value.histogram
                    })).filter(item => item.value !== null)
                };
                
            case 'EMA':
            case 'SMA':
                return data.map(item => ({
                    time: Math.floor(new Date(item.timestamp).getTime() / 1000),
                    value: item.value[indicatorName.toLowerCase()]
                })).filter(item => item.value !== null);
                
            default:
                return [];
        }
    }

    /**
     * Add trade marker
     */
    addTradeMarker(timestamp, price, type, side = null) {
        const time = Math.floor(new Date(timestamp).getTime() / 1000);
        
        let color, shape, text;
        
        if (type === 'entry') {
            color = side === 'buy' ? CONFIG.TRADE_COLORS.ENTRY_BUY : CONFIG.TRADE_COLORS.ENTRY_SELL;
            shape = side === 'buy' ? 'arrowUp' : 'arrowDown';
            text = side === 'buy' ? 'B' : 'S';
        } else {
            color = CONFIG.TRADE_COLORS.EXIT;
            shape = 'circle';
            text = 'X';
        }

        const marker = {
            time: time,
            position: type === 'entry' ? (side === 'buy' ? 'belowBar' : 'aboveBar') : 'inBar',
            color: color,
            shape: shape,
            text: text,
            size: 1
        };

        this.markers.push(marker);
        this.candlestickSeries.setMarkers(this.markers);
    }

    /**
     * Clear all markers
     */
    clearMarkers() {
        this.markers = [];
        this.candlestickSeries.setMarkers([]);
    }

    /**
     * Resize chart
     */
    resize() {
        const chartContainer = document.getElementById('chart');
        if (this.chart && chartContainer) {
            this.chart.applyOptions({
                width: chartContainer.clientWidth,
                height: CONFIG.CHART_HEIGHT
            });
        }

        // Resize indicator charts
        Object.values(this.indicatorCharts).forEach(chart => {
            const container = chart.chartElement().parentElement;
            if (container) {
                chart.applyOptions({
                    width: container.clientWidth,
                    height: CONFIG.INDICATOR_PANEL_HEIGHT
                });
            }
        });
    }

    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Window resize
        window.addEventListener('resize', UTILS.debounce(() => {
            this.resize();
        }, 250));

        // Chart controls
        document.getElementById('zoom-in-btn')?.addEventListener('click', () => {
            // Implement zoom in
        });

        document.getElementById('zoom-out-btn')?.addEventListener('click', () => {
            // Implement zoom out
        });

        document.getElementById('reset-zoom-btn')?.addEventListener('click', () => {
            this.chart.timeScale().fitContent();
        });
    }

    /**
     * Get current chart data
     */
    getCurrentData() {
        return this.currentData;
    }

    /**
     * Get last click data for trade marking
     */
    getLastClickData() {
        return this.lastClickData;
    }
}

// Create global chart manager instance
const chartManager = new ChartManager();

// Export for use in other modules
window.chartManager = chartManager;
