"""
MEXC API Service for Strategy Builder
"""
import requests
import pandas as pd
from datetime import datetime, timedelta
from typing import List, Dict, Optional
import logging
import time

from config.settings import settings

logger = logging.getLogger(__name__)

# Import binance_service for DataService
from .binance_service import binance_service


class MexcService:
    """MEXC API service for fetching OHLCV data"""
    
    def __init__(self):
        self.base_url = settings.mexc.base_url
        self.api_key = settings.mexc.api_key
        self.api_secret = settings.mexc.api_secret
        self.session = requests.Session()

        # Add API key to session headers if available
        if self.api_key:
            self.session.headers.update({
                'ApiKey': self.api_key
            })
        
        # Timeframe mapping
        self.timeframe_map = {
            '1m': '1m',
            '3m': '3m',
            '5m': '5m',
            '15m': '15m',
            '30m': '30m',
            '1h': '1h',
            '2h': '2h',
            '4h': '4h',
            '1d': '1d',
            '1w': '1w'
        }
    
    def get_klines(self, symbol: str, timeframe: str, limit: int = 500,
                   start_time: Optional[datetime] = None,
                   end_time: Optional[datetime] = None) -> List[Dict]:
        """
        Fetch klines (OHLCV) data from MEXC
        
        Args:
            symbol: Trading symbol (e.g., 'BTCUSDT')
            timeframe: Timeframe (e.g., '1h', '1d')
            limit: Number of candles to fetch (max 1000)
            start_time: Start time for data
            end_time: End time for data
            
        Returns:
            List of OHLCV dictionaries
        """
        try:
            # Validate inputs
            if timeframe not in self.timeframe_map:
                raise ValueError(f"Unsupported timeframe: {timeframe}")
            
            if limit > 1000:
                limit = 1000
                logger.warning("Limit reduced to 1000 (MEXC maximum)")
            
            # Prepare parameters
            params = {
                'symbol': symbol.upper(),
                'interval': self.timeframe_map[timeframe],
                'limit': limit
            }
            
            if start_time:
                params['startTime'] = int(start_time.timestamp() * 1000)
            if end_time:
                params['endTime'] = int(end_time.timestamp() * 1000)
            
            # Make API request
            url = f"{self.base_url}/api/v3/klines"
            response = self.session.get(url, params=params)
            response.raise_for_status()
            
            data = response.json()
            
            # Convert to OHLCV format
            ohlcv_data = []
            for kline in data:
                ohlcv_data.append({
                    'timestamp': datetime.fromtimestamp(int(kline[0]) / 1000),
                    'open': float(kline[1]),
                    'high': float(kline[2]),
                    'low': float(kline[3]),
                    'close': float(kline[4]),
                    'volume': float(kline[5])
                })
            
            logger.info(f"Fetched {len(ohlcv_data)} candles for {symbol} {timeframe} from MEXC")
            return ohlcv_data
            
        except requests.exceptions.RequestException as e:
            logger.error(f"MEXC API request error: {e}")
            raise
        except Exception as e:
            logger.error(f"Error fetching MEXC klines: {e}")
            raise
    
    def get_symbol_info(self, symbol: str) -> Dict:
        """Get symbol information"""
        try:
            url = f"{self.base_url}/api/v3/exchangeInfo"
            response = self.session.get(url)
            response.raise_for_status()
            
            data = response.json()
            
            for symbol_info in data['symbols']:
                if symbol_info['symbol'] == symbol.upper():
                    return {
                        'symbol': symbol_info['symbol'],
                        'status': symbol_info['status'],
                        'baseAsset': symbol_info['baseAsset'],
                        'quoteAsset': symbol_info['quoteAsset']
                    }
            
            raise ValueError(f"Symbol {symbol} not found")
            
        except Exception as e:
            logger.error(f"Error getting symbol info: {e}")
            raise
    
    def get_24hr_ticker(self, symbol: str) -> Dict:
        """Get 24hr ticker statistics"""
        try:
            url = f"{self.base_url}/api/v3/ticker/24hr"
            params = {'symbol': symbol.upper()}
            
            response = self.session.get(url, params=params)
            response.raise_for_status()
            
            data = response.json()
            
            return {
                'symbol': data['symbol'],
                'priceChange': float(data['priceChange']),
                'priceChangePercent': float(data['priceChangePercent']),
                'lastPrice': float(data['lastPrice']),
                'volume': float(data['volume']),
                'high': float(data['highPrice']),
                'low': float(data['lowPrice'])
            }
            
        except Exception as e:
            logger.error(f"Error getting 24hr ticker: {e}")
            raise
    
    def validate_symbol(self, symbol: str) -> bool:
        """Validate if symbol exists on MEXC"""
        try:
            self.get_symbol_info(symbol)
            return True
        except:
            return False
    
    def get_historical_data(self, symbol: str, timeframe: str, 
                          days_back: int = 30) -> List[Dict]:
        """
        Get historical data for specified number of days
        
        Args:
            symbol: Trading symbol
            timeframe: Timeframe
            days_back: Number of days to go back
            
        Returns:
            List of OHLCV dictionaries
        """
        try:
            end_time = datetime.now()
            start_time = end_time - timedelta(days=days_back)
            
            all_data = []
            current_start = start_time
            
            # Fetch data in chunks (MEXC limit is 1000 candles)
            while current_start < end_time:
                # Calculate chunk end time
                chunk_end = min(current_start + timedelta(days=5), end_time)
                
                # Fetch chunk
                chunk_data = self.get_klines(
                    symbol=symbol,
                    timeframe=timeframe,
                    limit=1000,
                    start_time=current_start,
                    end_time=chunk_end
                )
                
                all_data.extend(chunk_data)
                current_start = chunk_end
                
                # Rate limiting
                time.sleep(0.2)
            
            # Remove duplicates and sort
            df = pd.DataFrame(all_data)
            df = df.drop_duplicates(subset=['timestamp']).sort_values('timestamp')
            
            return df.to_dict('records')
            
        except Exception as e:
            logger.error(f"Error getting historical data: {e}")
            raise
    
    def get_server_time(self) -> datetime:
        """Get MEXC server time"""
        try:
            url = f"{self.base_url}/api/v3/time"
            response = self.session.get(url)
            response.raise_for_status()
            
            data = response.json()
            return datetime.fromtimestamp(data['serverTime'] / 1000)
            
        except Exception as e:
            logger.error(f"Error getting server time: {e}")
            raise
    
    def test_connection(self) -> bool:
        """Test connection to MEXC API"""
        try:
            # Add timeout to prevent hanging
            import requests
            response = self.session.get(
                f"{self.base_url}/api/v3/time",
                timeout=5  # 5 second timeout
            )
            if response.status_code == 200:
                logger.info("MEXC API connection successful")
                return True
            else:
                logger.error(f"MEXC API connection failed: {response.status_code}")
                return False
        except Exception as e:
            logger.error(f"MEXC API connection failed: {e}")
            return False


# Global instance
mexc_service = MexcService()


class DataService:
    """Unified data service for multiple exchanges"""
    
    def __init__(self):
        self.binance = binance_service
        self.mexc = mexc_service
        
        self.exchanges = {
            'binance': self.binance,
            'mexc': self.mexc
        }
    
    def get_klines(self, exchange: str, symbol: str, timeframe: str, 
                   limit: int = 500, start_time: Optional[datetime] = None,
                   end_time: Optional[datetime] = None) -> List[Dict]:
        """Get klines from specified exchange"""
        if exchange.lower() not in self.exchanges:
            raise ValueError(f"Unsupported exchange: {exchange}")
        
        service = self.exchanges[exchange.lower()]
        return service.get_klines(symbol, timeframe, limit, start_time, end_time)
    
    def validate_symbol(self, exchange: str, symbol: str) -> bool:
        """Validate symbol on specified exchange"""
        if exchange.lower() not in self.exchanges:
            return False
        
        service = self.exchanges[exchange.lower()]
        return service.validate_symbol(symbol)
    
    def test_connections(self) -> Dict[str, bool]:
        """Test connections to all exchanges"""
        results = {}
        for name, service in self.exchanges.items():
            results[name] = service.test_connection()
        return results


# Global unified data service
data_service = DataService()
