/**
 * Main Application Controller for Strategy Builder
 */

class StrategyBuilderApp {
    constructor() {
        this.isInitialized = false;
        this.connectionStatus = false;
        this.lastUpdate = null;
        
        this.init();
    }

    /**
     * Initialize the application
     */
    async init() {
        try {
            UTILS.showLoading('Initializing Strategy Builder...');
            
            // Test API connection
            await this.testConnection();
            
            // Setup event listeners
            this.setupEventListeners();
            
            // Initialize components
            this.initializeComponents();
            
            // Load initial data
            await this.loadInitialData();
            
            this.isInitialized = true;
            UTILS.hideLoading();
            
            UTILS.showNotification('Strategy Builder initialized successfully', 'success');
            this.updateConnectionStatus(true);
            
        } catch (error) {
            UTILS.hideLoading();
            UTILS.showNotification(`Initialization failed: ${error.message}`, 'error');
            this.updateConnectionStatus(false);
        }
    }

    /**
     * Test API connection
     */
    async testConnection() {
        try {
            const isConnected = await api.testConnection();
            if (!isConnected) {
                throw new Error('API connection failed');
            }
            this.connectionStatus = true;
        } catch (error) {
            this.connectionStatus = false;
            throw error;
        }
    }

    /**
     * Setup global event listeners
     */
    setupEventListeners() {
        // Load data button
        document.getElementById('load-data-btn')?.addEventListener('click', () => {
            this.loadChartData();
        });

        // Symbol input enter key
        document.getElementById('symbol-input')?.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.loadChartData();
            }
        });

        // Auto-load on timeframe change
        document.getElementById('timeframe-select')?.addEventListener('change', () => {
            if (this.isInitialized) {
                this.loadChartData();
            }
        });

        // Auto-load on exchange change
        document.getElementById('exchange-select')?.addEventListener('change', () => {
            if (this.isInitialized) {
                this.loadChartData();
            }
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            this.handleKeyboardShortcuts(e);
        });

        // Window beforeunload
        window.addEventListener('beforeunload', (e) => {
            this.handleBeforeUnload(e);
        });

        // Periodic connection check
        setInterval(() => {
            this.checkConnection();
        }, CONFIG.UPDATE_INTERVALS.SLOW);
    }

    /**
     * Initialize components
     */
    initializeComponents() {
        // Initialize indicators from UI state
        indicatorManager.initializeFromUI();
        
        // Update last update time
        this.updateLastUpdateTime();
    }

    /**
     * Load initial data
     */
    async loadInitialData() {
        // Load default symbol and timeframe
        const symbol = document.getElementById('symbol-input')?.value || CONFIG.DEFAULT_SYMBOL;
        const timeframe = document.getElementById('timeframe-select')?.value || CONFIG.DEFAULT_TIMEFRAME;
        const exchange = document.getElementById('exchange-select')?.value || CONFIG.DEFAULT_EXCHANGE;
        
        await this.loadChartData(exchange, symbol, timeframe);
    }

    /**
     * Load chart data
     */
    async loadChartData(exchange = null, symbol = null, timeframe = null) {
        try {
            // Get values from UI if not provided
            exchange = exchange || document.getElementById('exchange-select')?.value || CONFIG.DEFAULT_EXCHANGE;
            symbol = symbol || document.getElementById('symbol-input')?.value || CONFIG.DEFAULT_SYMBOL;
            timeframe = timeframe || document.getElementById('timeframe-select')?.value || CONFIG.DEFAULT_TIMEFRAME;
            const limit = parseInt(document.getElementById('candles-input')?.value) || CONFIG.DEFAULT_CANDLES;

            // Validate inputs
            if (!UTILS.validateSymbol(symbol)) {
                throw new Error('Invalid symbol format');
            }

            if (!UTILS.validateTimeframe(timeframe)) {
                throw new Error('Invalid timeframe');
            }

            // Update status
            this.updateDataStatus('Loading...');

            // Load OHLCV data
            await chartManager.loadData(exchange, symbol, timeframe, { limit });

            // Auto-calculate indicators if any are selected
            const selectedIndicators = indicatorManager.getSelectedIndicators();
            if (selectedIndicators.length > 0) {
                await indicatorManager.calculateSelectedIndicators();
            }

            // Update status
            this.updateDataStatus('Data loaded');
            this.updateLastUpdateTime();

        } catch (error) {
            this.updateDataStatus('Error loading data');
            UTILS.showNotification(`Error loading data: ${error.message}`, 'error');
        }
    }

    /**
     * Handle keyboard shortcuts
     */
    handleKeyboardShortcuts(e) {
        // Ctrl/Cmd + R: Refresh data
        if ((e.ctrlKey || e.metaKey) && e.key === 'r') {
            e.preventDefault();
            this.loadChartData();
        }

        // Ctrl/Cmd + N: New session
        if ((e.ctrlKey || e.metaKey) && e.key === 'n') {
            e.preventDefault();
            tradingManager.showNewSessionModal();
        }

        // Ctrl/Cmd + E: Export JSON
        if ((e.ctrlKey || e.metaKey) && e.key === 'e') {
            e.preventDefault();
            tradingManager.exportData('json');
        }

        // B: Mark buy entry (when chart is focused)
        if (e.key === 'b' && !e.ctrlKey && !e.metaKey && !this.isInputFocused()) {
            e.preventDefault();
            tradingManager.markEntry('buy');
        }

        // S: Mark sell entry (when chart is focused)
        if (e.key === 's' && !e.ctrlKey && !e.metaKey && !this.isInputFocused()) {
            e.preventDefault();
            tradingManager.markEntry('sell');
        }

        // X: Mark exit (when chart is focused)
        if (e.key === 'x' && !e.ctrlKey && !e.metaKey && !this.isInputFocused()) {
            e.preventDefault();
            tradingManager.markExit();
        }
    }

    /**
     * Check if an input element is focused
     */
    isInputFocused() {
        const activeElement = document.activeElement;
        return activeElement && (
            activeElement.tagName === 'INPUT' ||
            activeElement.tagName === 'TEXTAREA' ||
            activeElement.tagName === 'SELECT' ||
            activeElement.isContentEditable
        );
    }

    /**
     * Handle before unload
     */
    handleBeforeUnload(e) {
        // Save current state
        const preferences = {
            symbol: document.getElementById('symbol-input')?.value,
            timeframe: document.getElementById('timeframe-select')?.value,
            exchange: document.getElementById('exchange-select')?.value,
            candles: document.getElementById('candles-input')?.value
        };
        
        UTILS.saveToStorage(CONFIG.STORAGE_KEYS.USER_PREFERENCES, preferences);
    }

    /**
     * Check connection periodically
     */
    async checkConnection() {
        try {
            const isConnected = await api.testConnection();
            this.updateConnectionStatus(isConnected);
        } catch (error) {
            this.updateConnectionStatus(false);
        }
    }

    /**
     * Update connection status display
     */
    updateConnectionStatus(isConnected) {
        this.connectionStatus = isConnected;
        const statusElement = document.getElementById('connection-status');
        
        if (statusElement) {
            statusElement.textContent = isConnected ? 'Connected' : 'Disconnected';
            statusElement.className = isConnected ? 'status-connected' : 'status-disconnected';
        }
    }

    /**
     * Update data status display
     */
    updateDataStatus(status) {
        const statusElement = document.getElementById('data-status');
        if (statusElement) {
            statusElement.textContent = status;
        }
    }

    /**
     * Update last update time
     */
    updateLastUpdateTime() {
        this.lastUpdate = new Date();
        const statusElement = document.getElementById('last-update');
        
        if (statusElement) {
            statusElement.textContent = this.lastUpdate.toLocaleTimeString();
        }
    }

    /**
     * Get application status
     */
    getStatus() {
        return {
            initialized: this.isInitialized,
            connected: this.connectionStatus,
            lastUpdate: this.lastUpdate,
            currentSession: tradingManager.currentSession,
            openTrades: tradingManager.openTrades.size,
            totalTrades: tradingManager.tradeHistory.length
        };
    }

    /**
     * Refresh application
     */
    async refresh() {
        try {
            UTILS.showLoading('Refreshing...');
            
            // Reload chart data
            await this.loadChartData();
            
            // Recalculate indicators
            const selectedIndicators = indicatorManager.getSelectedIndicators();
            if (selectedIndicators.length > 0) {
                await indicatorManager.calculateSelectedIndicators();
            }
            
            // Reload session data
            if (tradingManager.currentSession) {
                await tradingManager.loadSessionData();
            }
            
            UTILS.hideLoading();
            UTILS.showNotification('Application refreshed', 'success');
            
        } catch (error) {
            UTILS.hideLoading();
            UTILS.showNotification(`Refresh failed: ${error.message}`, 'error');
        }
    }

    /**
     * Reset application
     */
    reset() {
        if (confirm('Are you sure you want to reset the application? This will clear all data.')) {
            // Clear storage
            UTILS.removeFromStorage(CONFIG.STORAGE_KEYS.CURRENT_SESSION);
            UTILS.removeFromStorage(CONFIG.STORAGE_KEYS.USER_PREFERENCES);
            
            // Reload page
            window.location.reload();
        }
    }
}

// Initialize application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    // Create global app instance
    window.app = new StrategyBuilderApp();
});

// Export for debugging
window.StrategyBuilderApp = StrategyBuilderApp;
