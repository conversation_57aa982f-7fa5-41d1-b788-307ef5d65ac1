[{"test": "GET /health", "status": "❌ FAIL", "success": false, "details": "Error: HTTPConnectionPool(host='localhost', port=8000): Read timed out. (read timeout=5)", "timestamp": "2025-07-27T22:35:03.065821"}, {"test": "GET /api/health", "status": "✅ PASS", "success": true, "details": "Service: strategy_builder_api, DB: connected", "timestamp": "2025-07-27T22:35:05.085030"}, {"test": "GET /api/config", "status": "✅ PASS", "success": true, "details": "BB length: 4, Indicators: 6", "timestamp": "2025-07-27T22:35:05.090031"}, {"test": "GET /api/exchanges", "status": "✅ PASS", "success": true, "details": "Exchanges: binance, mexc", "timestamp": "2025-07-27T22:35:05.096031"}, {"test": "GET /api/ohlcv/binance/BTCUSDT/1h", "status": "❌ FAIL", "success": false, "details": "Status code: 500", "timestamp": "2025-07-27T22:35:05.101039"}, {"test": "GET /api/ohlcv/mexc/ETHUSDT/4h", "status": "❌ FAIL", "success": false, "details": "Status code: 500", "timestamp": "2025-07-27T22:35:05.107032"}, {"test": "GET /api/indicators/supported", "status": "❌ FAIL", "success": false, "details": "Success=False in response", "timestamp": "2025-07-27T22:35:05.112035"}, {"test": "POST /api/indicators/calculate", "status": "❌ FAIL", "success": false, "details": "Status code: 422", "timestamp": "2025-07-27T22:35:05.118032"}, {"test": "POST /api/marks/entry", "status": "❌ FAIL", "success": false, "details": "Status code: 422", "timestamp": "2025-07-27T22:35:05.124031"}, {"test": "POST /api/marks/exit", "status": "❌ FAIL", "success": false, "details": "Status code: 422", "timestamp": "2025-07-27T22:35:05.130033"}, {"test": "POST /api/strategy/session/create", "status": "❌ FAIL", "success": false, "details": "Success=False in response", "timestamp": "2025-07-27T22:35:05.146034"}, {"test": "GET /api/strategy/sessions", "status": "❌ FAIL", "success": false, "details": "Success=False in response", "timestamp": "2025-07-27T22:35:05.154031"}, {"test": "GET /api/symbols/binance", "status": "✅ PASS", "success": true, "details": "Found 10 symbols", "timestamp": "2025-07-27T22:35:05.159035"}, {"test": "GET /api/symbols/mexc", "status": "✅ PASS", "success": true, "details": "Found 10 symbols", "timestamp": "2025-07-27T22:35:05.162030"}]