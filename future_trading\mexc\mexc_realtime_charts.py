#!/usr/bin/env python3
"""
Real-time MEXC TradingView-style charts with custom indicators
"""

import json
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import requests
import time
import hmac
import hashlib
import os
from urllib.parse import urlencode
# import websocket  # Not needed for this version
# import threading  # Not needed for this version
from technical_indicators import TechnicalIndicators
from config import MEXC_API_KEY, MEXC_SECRET_KEY, MEXC_BASE_URL

class MEXCDataProvider:
    """MEXC API data provider for real-time charts"""
    
    def __init__(self):
        self.api_key = MEXC_API_KEY
        self.secret_key = MEXC_SECRET_KEY
        self.base_url = MEXC_BASE_URL
        self.indicators = TechnicalIndicators()
        
    def _sign_request(self, params):
        """Sign MEXC API request"""
        query_string = urlencode(params)
        signature = hmac.new(
            self.secret_key.encode('utf-8'),
            query_string.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
        return signature
    
    def get_klines(self, symbol='BTC_USDT', interval='Min5', limit=500):
        """Get historical kline data from MEXC"""
        try:
            # MEXC API endpoint for klines (public endpoint)
            endpoint = f"https://contract.mexc.com/api/v1/contract/kline/{symbol}"

            # Map intervals to MEXC format
            interval_map = {
                'Min1': 'Min1',
                'Min5': 'Min5',
                'Min15': 'Min15',
                'Min30': 'Min30',
                'Hour1': 'Hour1',
                'Hour4': 'Hour4',
                'Day1': 'Day1',
                '1m': 'Min1',
                '5m': 'Min5',
                '1h': 'Hour1'
            }

            mexc_interval = interval_map.get(interval, interval)

            params = {
                'interval': mexc_interval,
                'limit': min(limit, 500)  # MEXC limit is 500
            }
            
            response = requests.get(endpoint, params=params, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                
                if data.get('success') and data.get('data'):
                    klines = data['data']
                    
                    # Convert to DataFrame
                    df = pd.DataFrame(klines, columns=[
                        'timestamp', 'open', 'high', 'low', 'close', 'volume'
                    ])
                    
                    # Convert timestamp to datetime
                    df['timestamp'] = pd.to_datetime(df['timestamp'], unit='s')
                    
                    # Convert price columns to float
                    for col in ['open', 'high', 'low', 'close', 'volume']:
                        df[col] = pd.to_numeric(df[col], errors='coerce')
                    
                    # Add technical indicators
                    df = self._add_indicators(df)
                    
                    print(f"✅ Retrieved {len(df)} {interval} candles for {symbol}")
                    return df
                else:
                    print(f"❌ MEXC API error: {data}")
                    return None
            else:
                print(f"❌ HTTP error {response.status_code}: {response.text}")
                return None
                
        except Exception as e:
            print(f"❌ Error fetching MEXC data: {e}")
            return None
    
    def _add_indicators(self, df):
        """Add technical indicators to DataFrame"""
        try:
            # Moving averages
            df['ema_20'] = self.indicators.ema(df['close'], 20)
            df['ema_50'] = self.indicators.ema(df['close'], 50)
            df['ema_200'] = self.indicators.ema(df['close'], 200)
            df['sma_20'] = self.indicators.sma(df['close'], 20)
            
            # Oscillators
            df['rsi'] = self.indicators.rsi(df['close'], 14)
            df['stoch_k'], df['stoch_d'] = self.indicators.stochastic(df['high'], df['low'], df['close'], 14, 3)
            
            # MACD
            df['macd'], df['macd_signal'], df['macd_histogram'] = self.indicators.macd(df['close'])
            
            # Bollinger Bands
            df['bb_upper'], df['bb_middle'], df['bb_lower'] = self.indicators.bollinger_bands(df['close'], 20, 2)
            
            # Volume indicators
            df['volume_sma'] = self.indicators.volume_sma(df['volume'], 20)
            df['volume_ratio'] = self.indicators.volume_ratio(df['volume'], 20)
            
            # Volatility
            df['atr'] = self.indicators.atr(df['high'], df['low'], df['close'], 14)
            
            # Support/Resistance levels
            df['pivot'] = (df['high'] + df['low'] + df['close']) / 3
            df['resistance_1'] = 2 * df['pivot'] - df['low']
            df['support_1'] = 2 * df['pivot'] - df['high']
            
            # Custom indicators
            df['price_momentum'] = df['close'].pct_change(5, fill_method=None) * 100  # 5-period momentum
            df['volume_momentum'] = df['volume'].pct_change(5, fill_method=None) * 100
            
            # Trend strength
            df['trend_strength'] = np.where(
                df['ema_20'] > df['ema_50'], 
                (df['ema_20'] - df['ema_50']) / df['ema_50'] * 100,
                (df['ema_20'] - df['ema_50']) / df['ema_50'] * 100
            )
            
            return df
            
        except Exception as e:
            print(f"❌ Error adding indicators: {e}")
            return df
    
    def get_ticker(self, symbol='BTC_USDT'):
        """Get current ticker data"""
        try:
            endpoint = f"{self.base_url}/api/v1/contract/ticker"
            params = {'symbol': symbol}
            
            response = requests.get(endpoint, params=params, timeout=5)
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success') and data.get('data'):
                    return data['data'][0] if isinstance(data['data'], list) else data['data']
            
            return None
            
        except Exception as e:
            print(f"❌ Error fetching ticker: {e}")
            return None

def create_realtime_chart_html(symbol='BTC_USDT', timeframes=['Min5', 'Min15']):
    """Create real-time TradingView-style chart with MEXC data"""
    
    mexc = MEXCDataProvider()
    
    # Get data for different timeframes
    data_dict = {}
    for tf in timeframes:
        df = mexc.get_klines(symbol, tf, 500)
        if df is not None:
            data_dict[tf] = df.to_json(orient='records', date_format='iso')
        else:
            print(f"❌ Failed to get data for {tf}")
            return None
    
    # Get current ticker
    ticker = mexc.get_ticker(symbol)
    current_price = float(ticker.get('lastPrice', 0)) if ticker else 0
    price_change = float(ticker.get('priceChangePercent', 0)) if ticker else 0
    volume_24h = float(ticker.get('volume', 0)) if ticker else 0
    
    html_content = f'''
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📊 MEXC Real-time Charts - {symbol}</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        * {{
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }}
        
        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #0d1421;
            color: #d1d4dc;
            line-height: 1.6;
            min-height: 100vh;
        }}
        
        .container {{
            max-width: 1800px;
            margin: 0 auto;
            padding: 10px;
        }}
        
        .header {{
            background: linear-gradient(135deg, #1e2329 0%, #2b3139 100%);
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 15px;
            border: 1px solid #363a45;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        }}
        
        .header h1 {{
            color: #ffffff;
            font-size: 2em;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 10px;
        }}
        
        .live-indicator {{
            background: #00d4aa;
            color: #000;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.6em;
            font-weight: bold;
            animation: pulse 2s infinite;
        }}
        
        @keyframes pulse {{
            0%, 100% {{ opacity: 1; }}
            50% {{ opacity: 0.7; }}
        }}
        
        .price-info {{
            display: flex;
            gap: 30px;
            align-items: center;
            margin-top: 10px;
        }}
        
        .current-price {{
            font-size: 1.8em;
            font-weight: bold;
            color: {'#00d4aa' if price_change >= 0 else '#ff6b6b'};
        }}
        
        .price-change {{
            font-size: 1.2em;
            color: {'#00d4aa' if price_change >= 0 else '#ff6b6b'};
        }}
        
        .volume-info {{
            color: #868993;
            font-size: 0.9em;
        }}
        
        .controls {{
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-bottom: 15px;
            flex-wrap: wrap;
        }}
        
        .control-btn {{
            background: #363a45;
            color: #d1d4dc;
            border: 1px solid #4a4e5a;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.9em;
            transition: all 0.3s ease;
            font-weight: 500;
        }}
        
        .control-btn:hover {{
            background: #4a4e5a;
            border-color: #5a5e6a;
            transform: translateY(-1px);
        }}
        
        .control-btn.active {{
            background: linear-gradient(135deg, #00d4aa 0%, #00b894 100%);
            color: #000;
            border-color: #00d4aa;
            box-shadow: 0 2px 8px rgba(0, 212, 170, 0.3);
        }}
        
        .chart-container {{
            background: linear-gradient(135deg, #1e2329 0%, #2b3139 100%);
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 15px;
            border: 1px solid #363a45;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        }}
        
        .chart-title {{
            color: #ffffff;
            font-size: 1.3em;
            margin-bottom: 15px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 8px;
        }}
        
        .main-chart {{
            height: 500px;
            margin-bottom: 10px;
        }}
        
        .indicator-chart {{
            height: 150px;
            margin-bottom: 10px;
        }}
        
        .indicators-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }}
        
        .indicator-panel {{
            background: #2b3139;
            border-radius: 8px;
            padding: 15px;
            border: 1px solid #363a45;
        }}
        
        .indicator-title {{
            color: #00d4aa;
            font-weight: bold;
            margin-bottom: 10px;
            font-size: 0.9em;
            text-transform: uppercase;
        }}
        
        .indicator-value {{
            font-size: 1.4em;
            font-weight: bold;
            margin-bottom: 5px;
        }}
        
        .indicator-label {{
            color: #868993;
            font-size: 0.8em;
        }}
        
        .refresh-btn {{
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: #00d4aa;
            color: #000;
            border: none;
            padding: 15px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 1.2em;
            box-shadow: 0 4px 12px rgba(0, 212, 170, 0.4);
            transition: all 0.3s ease;
        }}
        
        .refresh-btn:hover {{
            transform: scale(1.1);
            box-shadow: 0 6px 16px rgba(0, 212, 170, 0.6);
        }}
        
        @media (max-width: 768px) {{
            .container {{ padding: 5px; }}
            .price-info {{ flex-direction: column; gap: 10px; }}
            .main-chart {{ height: 350px; }}
            .indicator-chart {{ height: 120px; }}
        }}
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>
                📊 {symbol.replace('_', '/')} 
                <span class="live-indicator">LIVE</span>
            </h1>
            <div class="price-info">
                <div class="current-price">${current_price:,.2f}</div>
                <div class="price-change">
                    {'▲' if price_change >= 0 else '▼'} {price_change:+.2f}%
                </div>
                <div class="volume-info">
                    24h Volume: {volume_24h:,.0f}
                </div>
                <div class="volume-info">
                    Last Update: {datetime.now().strftime('%H:%M:%S')}
                </div>
            </div>
        </div>
        
        <!-- Controls -->
        <div class="controls">
            <button class="control-btn active" onclick="showTimeframe('Min5')">5M</button>
            <button class="control-btn" onclick="showTimeframe('Min15')">15M</button>
            <button class="control-btn" onclick="toggleIndicator('volume')">Volume</button>
            <button class="control-btn" onclick="toggleIndicator('rsi')">RSI</button>
            <button class="control-btn" onclick="toggleIndicator('macd')">MACD</button>
            <button class="control-btn" onclick="toggleIndicator('stoch')">Stochastic</button>
            <button class="control-btn" onclick="toggleOverlay('bb')">Bollinger Bands</button>
            <button class="control-btn" onclick="toggleOverlay('ema')">EMAs</button>
            <button class="control-btn" onclick="autoRefresh()">Auto Refresh</button>
        </div>
        
        <!-- Main Price Chart -->
        <div class="chart-container">
            <div class="chart-title">
                📈 Price Chart - Real-time MEXC Data
            </div>
            <div id="priceChart" class="main-chart"></div>
        </div>
        
        <!-- Volume Chart -->
        <div class="chart-container" id="volumePanel" style="display: none;">
            <div class="chart-title">📊 Volume</div>
            <div id="volumeChart" class="indicator-chart"></div>
        </div>
        
        <!-- RSI Chart -->
        <div class="chart-container" id="rsiPanel" style="display: none;">
            <div class="chart-title">📈 RSI (14)</div>
            <div id="rsiChart" class="indicator-chart"></div>
        </div>
        
        <!-- MACD Chart -->
        <div class="chart-container" id="macdPanel" style="display: none;">
            <div class="chart-title">📊 MACD</div>
            <div id="macdChart" class="indicator-chart"></div>
        </div>
        
        <!-- Stochastic Chart -->
        <div class="chart-container" id="stochPanel" style="display: none;">
            <div class="chart-title">📈 Stochastic</div>
            <div id="stochChart" class="indicator-chart"></div>
        </div>
        
        <!-- Indicators Grid -->
        <div class="indicators-grid">
            <div class="indicator-panel">
                <div class="indicator-title">RSI (14)</div>
                <div class="indicator-value" id="rsiValue">--</div>
                <div class="indicator-label">Current RSI Level</div>
            </div>
            <div class="indicator-panel">
                <div class="indicator-title">MACD</div>
                <div class="indicator-value" id="macdValue">--</div>
                <div class="indicator-label">MACD Line</div>
            </div>
            <div class="indicator-panel">
                <div class="indicator-title">Volume Ratio</div>
                <div class="indicator-value" id="volumeRatio">--</div>
                <div class="indicator-label">vs 20-period Average</div>
            </div>
            <div class="indicator-panel">
                <div class="indicator-title">Trend Strength</div>
                <div class="indicator-value" id="trendStrength">--</div>
                <div class="indicator-label">EMA20 vs EMA50</div>
            </div>
        </div>
    </div>
    
    <!-- Refresh Button -->
    <button class="refresh-btn" onclick="refreshData()" title="Refresh Data">
        🔄
    </button>

    <script>
        // Data from Python
        const chartData = {json.dumps(data_dict)};
        
        let currentTimeframe = 'Min5';
        let visibleIndicators = new Set();
        let visibleOverlays = new Set(['ema']);
        let autoRefreshInterval = null;
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {{
            createPriceChart();
            updateIndicatorValues();
        }});
        
        function showTimeframe(timeframe) {{
            currentTimeframe = timeframe;
            document.querySelectorAll('.control-btn').forEach(btn => {{
                if (btn.textContent === '5M' || btn.textContent === '15M') {{
                    btn.classList.remove('active');
                }}
            }});
            event.target.classList.add('active');
            updateAllCharts();
        }}
        
        function toggleIndicator(indicator) {{
            const panel = document.getElementById(indicator + 'Panel');
            if (visibleIndicators.has(indicator)) {{
                visibleIndicators.delete(indicator);
                panel.style.display = 'none';
                event.target.classList.remove('active');
            }} else {{
                visibleIndicators.add(indicator);
                panel.style.display = 'block';
                event.target.classList.add('active');
                createIndicatorChart(indicator);
            }}
        }}
        
        function toggleOverlay(overlay) {{
            if (visibleOverlays.has(overlay)) {{
                visibleOverlays.delete(overlay);
                event.target.classList.remove('active');
            }} else {{
                visibleOverlays.add(overlay);
                event.target.classList.add('active');
            }}
            createPriceChart();
        }}
        
        function updateAllCharts() {{
            createPriceChart();
            visibleIndicators.forEach(indicator => createIndicatorChart(indicator));
            updateIndicatorValues();
        }}
        
        function createPriceChart() {{
            const data = chartData[currentTimeframe];
            if (!data) return;
            
            const parsedData = JSON.parse(data);
            const traces = [];
            
            // Candlestick
            traces.push({{
                x: parsedData.map(d => d.timestamp),
                open: parsedData.map(d => d.open),
                high: parsedData.map(d => d.high),
                low: parsedData.map(d => d.low),
                close: parsedData.map(d => d.close),
                type: 'candlestick',
                name: '{symbol.replace('_', '/')}',
                increasing: {{fillcolor: '#00d4aa', line: {{color: '#00d4aa'}}}},
                decreasing: {{fillcolor: '#ff6b6b', line: {{color: '#ff6b6b'}}}}
            }});
            
            // EMAs overlay
            if (visibleOverlays.has('ema')) {{
                traces.push({{
                    x: parsedData.map(d => d.timestamp),
                    y: parsedData.map(d => d.ema_20),
                    type: 'scatter',
                    mode: 'lines',
                    name: 'EMA 20',
                    line: {{color: '#ffb74d', width: 1.5}},
                    opacity: 0.8
                }});
                
                traces.push({{
                    x: parsedData.map(d => d.timestamp),
                    y: parsedData.map(d => d.ema_50),
                    type: 'scatter',
                    mode: 'lines',
                    name: 'EMA 50',
                    line: {{color: '#ba68c8', width: 1.5}},
                    opacity: 0.8
                }});
            }}
            
            // Bollinger Bands overlay
            if (visibleOverlays.has('bb')) {{
                traces.push({{
                    x: parsedData.map(d => d.timestamp),
                    y: parsedData.map(d => d.bb_upper),
                    type: 'scatter',
                    mode: 'lines',
                    name: 'BB Upper',
                    line: {{color: '#64b5f6', width: 1, dash: 'dot'}},
                    opacity: 0.6
                }});
                
                traces.push({{
                    x: parsedData.map(d => d.timestamp),
                    y: parsedData.map(d => d.bb_lower),
                    type: 'scatter',
                    mode: 'lines',
                    name: 'BB Lower',
                    line: {{color: '#64b5f6', width: 1, dash: 'dot'}},
                    opacity: 0.6,
                    fill: 'tonexty',
                    fillcolor: 'rgba(100, 181, 246, 0.1)'
                }});
            }}
            
            const layout = {{
                plot_bgcolor: '#0d1421',
                paper_bgcolor: '#1e2329',
                font: {{color: '#d1d4dc', size: 11}},
                margin: {{l: 60, r: 60, t: 30, b: 40}},
                xaxis: {{
                    type: 'date',
                    gridcolor: '#363a45',
                    showgrid: true,
                    rangeslider: {{visible: false}}
                }},
                yaxis: {{
                    title: 'Price ($)',
                    gridcolor: '#363a45',
                    showgrid: true
                }},
                showlegend: true,
                legend: {{
                    x: 0,
                    y: 1,
                    bgcolor: 'rgba(30, 35, 41, 0.8)',
                    bordercolor: '#363a45',
                    borderwidth: 1
                }},
                hovermode: 'x unified'
            }};
            
            Plotly.newPlot('priceChart', traces, layout, {{responsive: true}});
        }}
        
        function createIndicatorChart(indicator) {{
            const data = chartData[currentTimeframe];
            if (!data) return;
            
            const parsedData = JSON.parse(data);
            const traces = [];
            let layout = {{
                plot_bgcolor: '#0d1421',
                paper_bgcolor: '#1e2329',
                font: {{color: '#d1d4dc', size: 10}},
                margin: {{l: 60, r: 60, t: 10, b: 30}},
                xaxis: {{
                    type: 'date',
                    gridcolor: '#363a45',
                    showgrid: true
                }},
                yaxis: {{
                    gridcolor: '#363a45',
                    showgrid: true
                }},
                showlegend: false,
                hovermode: 'x'
            }};
            
            if (indicator === 'volume') {{
                traces.push({{
                    x: parsedData.map(d => d.timestamp),
                    y: parsedData.map(d => d.volume),
                    type: 'bar',
                    name: 'Volume',
                    marker: {{
                        color: parsedData.map(d => d.close > d.open ? '#00d4aa' : '#ff6b6b'),
                        opacity: 0.7
                    }}
                }});
                layout.yaxis.title = 'Volume';
            }} else if (indicator === 'rsi') {{
                traces.push({{
                    x: parsedData.map(d => d.timestamp),
                    y: parsedData.map(d => d.rsi),
                    type: 'scatter',
                    mode: 'lines',
                    name: 'RSI',
                    line: {{color: '#9c27b0', width: 2}}
                }});
                
                // RSI levels
                traces.push({{
                    x: parsedData.map(d => d.timestamp),
                    y: Array(parsedData.length).fill(70),
                    type: 'scatter',
                    mode: 'lines',
                    line: {{color: '#ff6b6b', width: 1, dash: 'dash'}},
                    opacity: 0.5
                }});
                traces.push({{
                    x: parsedData.map(d => d.timestamp),
                    y: Array(parsedData.length).fill(30),
                    type: 'scatter',
                    mode: 'lines',
                    line: {{color: '#00d4aa', width: 1, dash: 'dash'}},
                    opacity: 0.5
                }});
                
                layout.yaxis.title = 'RSI';
                layout.yaxis.range = [0, 100];
            }} else if (indicator === 'macd') {{
                traces.push({{
                    x: parsedData.map(d => d.timestamp),
                    y: parsedData.map(d => d.macd),
                    type: 'scatter',
                    mode: 'lines',
                    name: 'MACD',
                    line: {{color: '#2196f3', width: 1.5}}
                }});
                traces.push({{
                    x: parsedData.map(d => d.timestamp),
                    y: parsedData.map(d => d.macd_signal),
                    type: 'scatter',
                    mode: 'lines',
                    name: 'Signal',
                    line: {{color: '#ff9800', width: 1.5}}
                }});
                traces.push({{
                    x: parsedData.map(d => d.timestamp),
                    y: parsedData.map(d => d.macd_histogram),
                    type: 'bar',
                    name: 'Histogram',
                    marker: {{
                        color: parsedData.map(d => d.macd_histogram > 0 ? '#00d4aa' : '#ff6b6b'),
                        opacity: 0.6
                    }}
                }});
                layout.yaxis.title = 'MACD';
            }} else if (indicator === 'stoch') {{
                traces.push({{
                    x: parsedData.map(d => d.timestamp),
                    y: parsedData.map(d => d.stoch_k),
                    type: 'scatter',
                    mode: 'lines',
                    name: '%K',
                    line: {{color: '#00d4aa', width: 2}}
                }});
                traces.push({{
                    x: parsedData.map(d => d.timestamp),
                    y: parsedData.map(d => d.stoch_d),
                    type: 'scatter',
                    mode: 'lines',
                    name: '%D',
                    line: {{color: '#ff6b6b', width: 2}}
                }});
                
                // Stochastic levels
                traces.push({{
                    x: parsedData.map(d => d.timestamp),
                    y: Array(parsedData.length).fill(80),
                    type: 'scatter',
                    mode: 'lines',
                    line: {{color: '#868993', width: 1, dash: 'dash'}},
                    opacity: 0.3
                }});
                traces.push({{
                    x: parsedData.map(d => d.timestamp),
                    y: Array(parsedData.length).fill(20),
                    type: 'scatter',
                    mode: 'lines',
                    line: {{color: '#868993', width: 1, dash: 'dash'}},
                    opacity: 0.3
                }});
                
                layout.yaxis.title = 'Stochastic';
                layout.yaxis.range = [0, 100];
            }}
            
            Plotly.newPlot(indicator + 'Chart', traces, layout, {{responsive: true}});
        }}
        
        function updateIndicatorValues() {{
            const data = chartData[currentTimeframe];
            if (!data) return;
            
            const parsedData = JSON.parse(data);
            const latest = parsedData[parsedData.length - 1];
            
            // Update indicator values
            document.getElementById('rsiValue').textContent = latest.rsi ? latest.rsi.toFixed(1) : '--';
            document.getElementById('macdValue').textContent = latest.macd ? latest.macd.toFixed(4) : '--';
            document.getElementById('volumeRatio').textContent = latest.volume_ratio ? latest.volume_ratio.toFixed(2) + 'x' : '--';
            document.getElementById('trendStrength').textContent = latest.trend_strength ? latest.trend_strength.toFixed(2) + '%' : '--';
            
            // Color coding
            const rsiElement = document.getElementById('rsiValue');
            if (latest.rsi) {{
                if (latest.rsi > 70) rsiElement.style.color = '#ff6b6b';
                else if (latest.rsi < 30) rsiElement.style.color = '#00d4aa';
                else rsiElement.style.color = '#d1d4dc';
            }}
            
            const trendElement = document.getElementById('trendStrength');
            if (latest.trend_strength) {{
                trendElement.style.color = latest.trend_strength > 0 ? '#00d4aa' : '#ff6b6b';
            }}
        }}
        
        function refreshData() {{
            location.reload();
        }}
        
        function autoRefresh() {{
            if (autoRefreshInterval) {{
                clearInterval(autoRefreshInterval);
                autoRefreshInterval = null;
                event.target.classList.remove('active');
                event.target.textContent = 'Auto Refresh';
            }} else {{
                autoRefreshInterval = setInterval(refreshData, 30000); // 30 seconds
                event.target.classList.add('active');
                event.target.textContent = 'Auto Refresh (ON)';
            }}
        }}
    </script>
</body>
</html>
'''
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"mexc_realtime_chart_{symbol}_{timestamp}.html"
    
    with open(filename, 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print(f"✅ Real-time MEXC chart created: {filename}")
    return filename

def main():
    """Main function"""
    print("="*80)
    print("📊 MEXC REAL-TIME TRADINGVIEW-STYLE CHARTS")
    print("="*80)
    
    symbol = 'BTC_USDT'
    timeframes = ['Min5', 'Min15']
    
    print(f"🔄 Creating real-time chart for {symbol}...")
    print(f"📊 Timeframes: {', '.join(timeframes)}")
    
    filename = create_realtime_chart_html(symbol, timeframes)
    
    if filename:
        print(f"\n🎉 REAL-TIME CHART CREATED!")
        print(f"📁 File: {filename}")
        print(f"\n🚀 Features:")
        print(f"   ✅ Real-time MEXC data")
        print(f"   ✅ Multiple timeframes (5M, 1H)")
        print(f"   ✅ Custom indicators (RSI, MACD, Stochastic)")
        print(f"   ✅ Volume analysis")
        print(f"   ✅ Bollinger Bands & EMAs")
        print(f"   ✅ Auto-refresh capability")
        print(f"   ✅ Professional TradingView styling")
        print(f"   ✅ Live price updates")
        
        try:
            import webbrowser
            full_path = os.path.abspath(filename)
            webbrowser.open(f'file://{full_path}')
            print(f"🌐 Opening in browser...")
        except:
            print(f"💡 Open manually: {os.path.abspath(filename)}")

if __name__ == "__main__":
    main()
