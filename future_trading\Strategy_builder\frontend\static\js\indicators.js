/**
 * Indicators Management for Strategy Builder
 */

class IndicatorManager {
    constructor() {
        this.activeIndicators = new Map();
        this.indicatorConfigs = new Map();
        this.loadDefaultConfigs();
        this.setupEventListeners();
    }

    /**
     * Load default indicator configurations
     */
    loadDefaultConfigs() {
        // Load from storage or use defaults
        const savedConfigs = UTILS.getFromStorage(CONFIG.STORAGE_KEYS.INDICATOR_CONFIGS, {});

        Object.keys(CONFIG.DEFAULT_INDICATOR_PARAMS).forEach(indicator => {
            const config = savedConfigs[indicator] || CONFIG.DEFAULT_INDICATOR_PARAMS[indicator];
            this.indicatorConfigs.set(indicator, config);
        });

        // Auto-load default indicators
        this.loadDefaultIndicators();
    }

    /**
     * Load default indicators automatically
     */
    loadDefaultIndicators() {
        if (CONFIG.DEFAULT_INDICATORS && CONFIG.DEFAULT_INDICATORS.length > 0) {
            CONFIG.DEFAULT_INDICATORS.forEach(indicator => {
                // Check the checkbox for this indicator
                const checkbox = document.querySelector(`input[data-indicator="${indicator}"]`);
                if (checkbox) {
                    checkbox.checked = true;
                    this.toggleIndicator(indicator, true);
                }
            });
        }
    }

    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Calculate indicators button
        document.getElementById('calculate-indicators-btn')?.addEventListener('click', () => {
            this.calculateSelectedIndicators();
        });

        // Indicator checkboxes
        document.querySelectorAll('.indicator-item input[type="checkbox"]').forEach(checkbox => {
            checkbox.addEventListener('change', (e) => {
                const indicator = this.getIndicatorFromCheckbox(e.target);
                if (indicator) {
                    this.toggleIndicator(indicator, e.target.checked);
                }
            });
        });

        // Configuration buttons
        document.querySelectorAll('.config-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const indicator = e.target.dataset.indicator;
                if (indicator) {
                    this.showIndicatorConfig(indicator);
                }
            });
        });
    }

    /**
     * Get indicator name from checkbox
     */
    getIndicatorFromCheckbox(checkbox) {
        const id = checkbox.id;
        if (id.includes('rsi')) return 'RSI';
        if (id.includes('macd')) return 'MACD';
        if (id.includes('ema')) return 'EMA';
        if (id.includes('sma')) return 'SMA';
        if (id.includes('bb')) return 'BB';
        if (id.includes('stoch')) return 'STOCH';
        return null;
    }

    /**
     * Toggle indicator on/off
     */
    toggleIndicator(indicator, enabled) {
        if (enabled) {
            this.activeIndicators.set(indicator, true);
        } else {
            this.activeIndicators.delete(indicator);
            this.removeIndicatorFromChart(indicator);
        }
    }

    /**
     * Calculate selected indicators
     */
    async calculateSelectedIndicators() {
        try {
            const selectedIndicators = this.getSelectedIndicators();
            
            if (selectedIndicators.length === 0) {
                UTILS.showNotification('No indicators selected', 'warning');
                return;
            }

            UTILS.showLoading('Calculating indicators...');

            const response = await api.calculateIndicators(
                chartManager.currentSymbol,
                chartManager.currentTimeframe,
                selectedIndicators
            );

            if (response.indicators) {
                this.displayIndicators(response.indicators);
                UTILS.showNotification(CONFIG.SUCCESS_MESSAGES.INDICATORS_CALCULATED, 'success');
            }

            UTILS.hideLoading();
        } catch (error) {
            UTILS.hideLoading();
            UTILS.showNotification(`Error calculating indicators: ${error.message}`, 'error');
        }
    }

    /**
     * Get selected indicators with configurations
     */
    getSelectedIndicators() {
        const selected = [];
        
        this.activeIndicators.forEach((enabled, indicator) => {
            if (enabled) {
                const config = this.indicatorConfigs.get(indicator) || {};
                selected.push({
                    name: indicator,
                    params: config
                });
            }
        });

        return selected;
    }

    /**
     * Display indicators on chart
     */
    displayIndicators(indicatorsData) {
        Object.keys(indicatorsData).forEach(indicatorName => {
            const data = indicatorsData[indicatorName];
            
            if (this.isOverlayIndicator(indicatorName)) {
                chartManager.addIndicatorOverlay(indicatorName, data);
            } else {
                chartManager.addIndicatorPanel(indicatorName, data);
            }
        });
    }

    /**
     * Check if indicator is overlay type
     */
    isOverlayIndicator(indicator) {
        return ['EMA', 'SMA', 'BB'].includes(indicator);
    }

    /**
     * Remove indicator from chart
     */
    removeIndicatorFromChart(indicator) {
        if (this.isOverlayIndicator(indicator)) {
            // Remove overlay series
            const series = chartManager.indicatorSeries[indicator];
            if (series) {
                chartManager.chart.removeSeries(series);
                delete chartManager.indicatorSeries[indicator];
            }
        } else {
            // Hide indicator panel
            const panelId = `${indicator.toLowerCase()}-panel`;
            const panel = document.getElementById(panelId);
            if (panel) {
                panel.style.display = 'none';
            }
            
            // Remove indicator chart
            const indicatorChart = chartManager.indicatorCharts[indicator];
            if (indicatorChart) {
                indicatorChart.remove();
                delete chartManager.indicatorCharts[indicator];
            }
        }
    }

    /**
     * Show indicator configuration modal
     */
    showIndicatorConfig(indicator) {
        const modal = document.getElementById('indicator-config-modal');
        const title = document.getElementById('indicator-config-title');
        const body = document.getElementById('indicator-config-body');
        
        if (!modal || !title || !body) return;

        title.textContent = `Configure ${indicator}`;
        body.innerHTML = this.generateConfigForm(indicator);
        
        modal.style.display = 'block';
        
        // Setup save button
        document.getElementById('save-indicator-config-btn').onclick = () => {
            this.saveIndicatorConfig(indicator);
            modal.style.display = 'none';
        };
    }

    /**
     * Generate configuration form for indicator
     */
    generateConfigForm(indicator) {
        const config = this.indicatorConfigs.get(indicator) || {};
        let html = '';

        switch (indicator) {
            case 'RSI':
                html = `
                    <div class="form-group">
                        <label for="rsi-length">Period:</label>
                        <input type="number" id="rsi-length" value="${config.length || 14}" min="1" max="100">
                    </div>
                `;
                break;
                
            case 'MACD':
                html = `
                    <div class="form-group">
                        <label for="macd-fast">Fast Period:</label>
                        <input type="number" id="macd-fast" value="${config.fast || 12}" min="1" max="50">
                    </div>
                    <div class="form-group">
                        <label for="macd-slow">Slow Period:</label>
                        <input type="number" id="macd-slow" value="${config.slow || 26}" min="1" max="100">
                    </div>
                    <div class="form-group">
                        <label for="macd-signal">Signal Period:</label>
                        <input type="number" id="macd-signal" value="${config.signal || 9}" min="1" max="50">
                    </div>
                `;
                break;
                
            case 'EMA':
            case 'SMA':
                html = `
                    <div class="form-group">
                        <label for="${indicator.toLowerCase()}-length">Period:</label>
                        <input type="number" id="${indicator.toLowerCase()}-length" value="${config.length || 20}" min="1" max="200">
                    </div>
                `;
                break;
                
            case 'BB':
                html = `
                    <div class="form-group">
                        <label for="bb-length">Period:</label>
                        <input type="number" id="bb-length" value="${config.length || 20}" min="1" max="100">
                    </div>
                    <div class="form-group">
                        <label for="bb-std">Standard Deviation:</label>
                        <input type="number" id="bb-std" value="${config.std || 2}" min="0.1" max="5" step="0.1">
                    </div>
                `;
                break;
                
            case 'STOCH':
                html = `
                    <div class="form-group">
                        <label for="stoch-k">%K Period:</label>
                        <input type="number" id="stoch-k" value="${config.k || 14}" min="1" max="100">
                    </div>
                    <div class="form-group">
                        <label for="stoch-d">%D Period:</label>
                        <input type="number" id="stoch-d" value="${config.d || 3}" min="1" max="50">
                    </div>
                    <div class="form-group">
                        <label for="stoch-smooth">Smooth %K:</label>
                        <input type="number" id="stoch-smooth" value="${config.smooth_k || 3}" min="1" max="50">
                    </div>
                `;
                break;
        }

        return html;
    }

    /**
     * Save indicator configuration
     */
    saveIndicatorConfig(indicator) {
        let config = {};

        switch (indicator) {
            case 'RSI':
                config.length = parseInt(document.getElementById('rsi-length').value);
                break;
                
            case 'MACD':
                config.fast = parseInt(document.getElementById('macd-fast').value);
                config.slow = parseInt(document.getElementById('macd-slow').value);
                config.signal = parseInt(document.getElementById('macd-signal').value);
                break;
                
            case 'EMA':
            case 'SMA':
                config.length = parseInt(document.getElementById(`${indicator.toLowerCase()}-length`).value);
                break;
                
            case 'BB':
                config.length = parseInt(document.getElementById('bb-length').value);
                config.std = parseFloat(document.getElementById('bb-std').value);
                break;
                
            case 'STOCH':
                config.k = parseInt(document.getElementById('stoch-k').value);
                config.d = parseInt(document.getElementById('stoch-d').value);
                config.smooth_k = parseInt(document.getElementById('stoch-smooth').value);
                break;
        }

        // Save configuration
        this.indicatorConfigs.set(indicator, config);
        
        // Save to storage
        const allConfigs = {};
        this.indicatorConfigs.forEach((config, name) => {
            allConfigs[name] = config;
        });
        UTILS.saveToStorage(CONFIG.STORAGE_KEYS.INDICATOR_CONFIGS, allConfigs);

        // Update label
        this.updateIndicatorLabel(indicator, config);
        
        UTILS.showNotification(`${indicator} configuration saved`, 'success');
    }

    /**
     * Update indicator label with current parameters
     */
    updateIndicatorLabel(indicator, config) {
        const checkbox = document.querySelector(`#${indicator.toLowerCase()}-checkbox`);
        if (!checkbox) return;

        const label = checkbox.nextElementSibling;
        if (!label) return;

        let paramText = '';
        switch (indicator) {
            case 'RSI':
                paramText = `(${config.length})`;
                break;
            case 'MACD':
                paramText = `(${config.fast},${config.slow},${config.signal})`;
                break;
            case 'EMA':
            case 'SMA':
                paramText = `(${config.length})`;
                break;
            case 'BB':
                paramText = `(${config.length},${config.std})`;
                break;
            case 'STOCH':
                paramText = `(${config.k},${config.d},${config.smooth_k})`;
                break;
        }

        label.textContent = `${indicator} ${paramText}`;
    }

    /**
     * Get indicator snapshot for trade marking
     */
    async getIndicatorSnapshot(timestamp) {
        try {
            const selectedIndicators = this.getSelectedIndicators();
            
            if (selectedIndicators.length === 0) {
                return {};
            }

            const response = await api.getIndicatorSnapshot(
                chartManager.currentSymbol,
                chartManager.currentTimeframe,
                timestamp,
                selectedIndicators
            );

            return response.indicators || {};
        } catch (error) {
            console.error('Error getting indicator snapshot:', error);
            return {};
        }
    }

    /**
     * Initialize indicators from checkboxes
     */
    initializeFromUI() {
        document.querySelectorAll('.indicator-item input[type="checkbox"]').forEach(checkbox => {
            if (checkbox.checked) {
                const indicator = this.getIndicatorFromCheckbox(checkbox);
                if (indicator) {
                    this.activeIndicators.set(indicator, true);
                }
            }
        });
    }
}

// Create global indicator manager instance
const indicatorManager = new IndicatorManager();

// Export for use in other modules
window.indicatorManager = indicatorManager;
