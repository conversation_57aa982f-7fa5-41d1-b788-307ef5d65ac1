"""
Database configuration and connection management
Now using PyMySQL for direct database operations
"""
import logging
from typing import Dict, Any

from .pymysql_db import db_manager

# Configure logging
logger = logging.getLogger(__name__)


def test_connection() -> bool:
    """Test database connection"""
    return db_manager.test_connection()


def get_database_health() -> Dict[str, Any]:
    """Get database health information"""
    return db_manager.get_health_check()


def get_db_session():
    """Get database connection context manager"""
    return db_manager.get_connection()


# For backward compatibility
def health_check() -> Dict[str, Any]:
    """Alias for get_database_health"""
    return get_database_health()


# Export commonly used functions
__all__ = [
    'test_connection',
    'get_database_health',
    'health_check',
    'get_db_session',
    'db_manager'
]
