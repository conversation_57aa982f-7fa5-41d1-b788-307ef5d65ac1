/**
 * API Client for Strategy Builder
 */

class APIClient {
    constructor() {
        this.baseURL = CONFIG.API_BASE_URL;
        this.defaultHeaders = {
            'Content-Type': 'application/json',
        };
    }

    /**
     * Make HTTP request
     */
    async request(endpoint, options = {}) {
        const url = `${this.baseURL}${endpoint}`;
        const config = {
            headers: { ...this.defaultHeaders, ...options.headers },
            ...options
        };

        try {
            const response = await fetch(url, config);
            
            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new Error(errorData.detail || `HTTP ${response.status}: ${response.statusText}`);
            }

            // Handle different response types
            const contentType = response.headers.get('content-type');
            if (contentType && contentType.includes('application/json')) {
                return await response.json();
            } else if (contentType && contentType.includes('text/')) {
                return await response.text();
            } else {
                return response;
            }
        } catch (error) {
            console.error(`API Request failed: ${endpoint}`, error);
            throw error;
        }
    }

    /**
     * GET request
     */
    async get(endpoint, params = {}) {
        const queryString = new URLSearchParams(params).toString();
        const url = queryString ? `${endpoint}?${queryString}` : endpoint;
        return this.request(url, { method: 'GET' });
    }

    /**
     * POST request
     */
    async post(endpoint, data = {}) {
        return this.request(endpoint, {
            method: 'POST',
            body: JSON.stringify(data)
        });
    }

    /**
     * DELETE request
     */
    async delete(endpoint) {
        return this.request(endpoint, { method: 'DELETE' });
    }

    // OHLCV Data Methods
    async getOHLCVData(exchange, symbol, timeframe, options = {}) {
        const params = {
            limit: options.limit || CONFIG.DEFAULT_CANDLES,
            force_refresh: options.forceRefresh || false,
            ...options
        };
        
        return this.get(`${CONFIG.ENDPOINTS.OHLCV}/${exchange}/${symbol}/${timeframe}`, params);
    }

    async getLatestCandle(exchange, symbol, timeframe) {
        return this.get(`${CONFIG.ENDPOINTS.OHLCV}/${exchange}/${symbol}/${timeframe}/latest`);
    }

    // Indicators Methods
    async calculateIndicators(symbol, timeframe, indicators, limit = 500) {
        const data = {
            symbol,
            timeframe,
            indicators,
            limit
        };
        
        return this.post(`${CONFIG.ENDPOINTS.INDICATORS}/calculate`, data);
    }

    async getSupportedIndicators() {
        return this.get(`${CONFIG.ENDPOINTS.INDICATORS}/supported`);
    }

    async getIndicatorSnapshot(symbol, timeframe, timestamp, indicators) {
        const data = {
            symbol,
            timeframe,
            timestamp,
            indicators
        };
        
        return this.post(`${CONFIG.ENDPOINTS.INDICATORS}/snapshot`, data);
    }

    // Marks Methods
    async createEntryMark(symbol, timeframe, timestamp, price, entrySide, sessionId, snapshots = {}) {
        const data = {
            symbol,
            timeframe,
            timestamp,
            price,
            entry_side: entrySide,
            session_id: sessionId,
            indicator_snapshot: snapshots.indicators || {},
            ohlcv_snapshot: snapshots.ohlcv || {}
        };
        
        return this.post(`${CONFIG.ENDPOINTS.MARKS}/entry`, data);
    }

    async createExitMark(symbol, timeframe, timestamp, price, linkedTradeId, sessionId, snapshots = {}) {
        const data = {
            symbol,
            timeframe,
            timestamp,
            price,
            linked_trade_id: linkedTradeId,
            session_id: sessionId,
            indicator_snapshot: snapshots.indicators || {},
            ohlcv_snapshot: snapshots.ohlcv || {}
        };
        
        return this.post(`${CONFIG.ENDPOINTS.MARKS}/exit`, data);
    }

    async getSessionMarks(sessionId) {
        return this.get(`${CONFIG.ENDPOINTS.MARKS}/session/${sessionId}`);
    }

    async getOpenEntries(sessionId) {
        return this.get(`${CONFIG.ENDPOINTS.MARKS}/open-entries/${sessionId}`);
    }

    // Strategy Methods
    async createSession(symbol, timeframe, sessionName = null) {
        const data = {
            symbol,
            timeframe,
            session_name: sessionName
        };
        
        return this.post(`${CONFIG.ENDPOINTS.STRATEGY}/session/create`, data);
    }

    async getSession(sessionId) {
        return this.get(`${CONFIG.ENDPOINTS.STRATEGY}/session/${sessionId}`);
    }

    async listSessions(filters = {}) {
        return this.get(`${CONFIG.ENDPOINTS.STRATEGY}/sessions`, filters);
    }

    async getSessionAnalytics(sessionId) {
        return this.get(`${CONFIG.ENDPOINTS.STRATEGY}/analytics/${sessionId}`);
    }

    async exportSessionJSON(sessionId) {
        const response = await this.request(`${CONFIG.ENDPOINTS.STRATEGY}/export/${sessionId}/json`, {
            method: 'GET'
        });
        
        // Handle file download
        if (response instanceof Response) {
            const blob = await response.blob();
            this.downloadFile(blob, `strategy_session_${sessionId}.json`, 'application/json');
        }
        
        return response;
    }

    async exportSessionCSV(sessionId) {
        const response = await this.request(`${CONFIG.ENDPOINTS.STRATEGY}/export/${sessionId}/csv`, {
            method: 'GET'
        });
        
        // Handle file download
        if (response instanceof Response) {
            const blob = await response.blob();
            this.downloadFile(blob, `strategy_trades_${sessionId}.csv`, 'text/csv');
        }
        
        return response;
    }

    // System Methods
    async getConfig() {
        return this.get(CONFIG.ENDPOINTS.CONFIG);
    }

    async getHealth() {
        return this.get(CONFIG.ENDPOINTS.HEALTH);
    }

    async getExchanges() {
        return this.get('/exchanges');
    }

    async getSymbols(exchange) {
        return this.get(`/symbols/${exchange}`);
    }

    /**
     * Download file helper
     */
    downloadFile(blob, filename, mimeType) {
        const url = window.URL.createObjectURL(new Blob([blob], { type: mimeType }));
        const link = document.createElement('a');
        link.href = url;
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
    }

    /**
     * Test API connection
     */
    async testConnection() {
        try {
            const health = await this.getHealth();
            return health.status === 'healthy';
        } catch (error) {
            console.error('API connection test failed:', error);
            return false;
        }
    }
}

// Create global API client instance
const api = new APIClient();

// Export for use in other modules
window.api = api;
