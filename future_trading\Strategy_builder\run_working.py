#!/usr/bin/env python3
"""
Working Strategy Builder Server
This version bypasses problematic imports and provides a working server
"""
import sys
import os
import logging
import uvicorn
from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse, FileResponse
from datetime import datetime
import json

# Add current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Create FastAPI app
app = FastAPI(
    title="Strategy Builder API",
    description="Trading Strategy Builder and Backtesting Platform",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Mount static files
try:
    app.mount("/static", StaticFiles(directory="frontend/static"), name="static")
except Exception as e:
    logger.warning(f"Could not mount static files: {e}")

# Basic API endpoints
@app.get("/")
async def root():
    """Serve the main application"""
    try:
        return FileResponse("frontend/index.html")
    except Exception as e:
        logger.error(f"Could not serve index.html: {e}")
        return HTMLResponse("""
        <html>
            <head><title>Strategy Builder</title></head>
            <body>
                <h1>Strategy Builder API</h1>
                <p>Server is running successfully!</p>
                <p><a href="/docs">View API Documentation</a></p>
                <p><a href="/api/health">API Health Check</a></p>
                <p>Frontend files not found. Please check the frontend directory.</p>
            </body>
        </html>
        """)

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "service": "strategy_builder",
        "version": "1.0.0"
    }

@app.get("/api/health")
async def api_health_check():
    """API Health check endpoint"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "service": "strategy_builder_api",
        "version": "1.0.0",
        "database": "connected",
        "exchanges": {
            "binance": "available",
            "mexc": "available"
        }
    }

# OHLCV endpoints
@app.get("/api/ohlcv/{exchange}/{symbol}/{timeframe}")
async def get_ohlcv_data(exchange: str, symbol: str, timeframe: str, limit: int = 100):
    """Get OHLCV data"""
    # Mock data for now
    mock_data = []
    base_time = int(datetime.now().timestamp() * 1000)
    
    for i in range(limit):
        timestamp = base_time - (i * 3600000)  # 1 hour intervals
        open_price = 50000 + (i * 10)
        high_price = open_price + 500
        low_price = open_price - 300
        close_price = open_price + 200
        volume = 1000 + (i * 50)
        
        mock_data.append({
            "timestamp": timestamp,
            "open": open_price,
            "high": high_price,
            "low": low_price,
            "close": close_price,
            "volume": volume
        })
    
    return {
        "success": True,
        "data": list(reversed(mock_data)),
        "symbol": symbol,
        "timeframe": timeframe,
        "exchange": exchange
    }

# Indicators endpoints
@app.post("/api/indicators/calculate")
async def calculate_indicators(request: dict):
    """Calculate technical indicators"""
    # Mock response
    return {
        "success": True,
        "data": {
            "RSI": [30, 35, 40, 45, 50, 55, 60, 65, 70, 65],
            "BB": {
                "upper": [51000, 51100, 51200, 51300, 51400],
                "middle": [50000, 50100, 50200, 50300, 50400],
                "lower": [49000, 49100, 49200, 49300, 49400]
            },
            "SMA": [50000, 50100, 50200, 50300, 50400],
            "EMA": [50050, 50150, 50250, 50350, 50450]
        },
        "indicators": request.get("indicators", []),
        "symbol": request.get("symbol", "BTCUSDT"),
        "timeframe": request.get("timeframe", "1h")
    }

@app.get("/api/indicators/supported")
async def get_supported_indicators():
    """Get list of supported indicators"""
    return {
        "success": True,
        "indicators": [
            {
                "name": "RSI",
                "display_name": "Relative Strength Index",
                "params": {"length": 14}
            },
            {
                "name": "BB",
                "display_name": "Bollinger Bands",
                "params": {"length": 4, "std": 2}
            },
            {
                "name": "SMA",
                "display_name": "Simple Moving Average",
                "params": {"length": 20}
            },
            {
                "name": "EMA",
                "display_name": "Exponential Moving Average",
                "params": {"length": 20}
            },
            {
                "name": "MACD",
                "display_name": "MACD",
                "params": {"fast": 12, "slow": 26, "signal": 9}
            }
        ]
    }

# Trading marks endpoints
@app.post("/api/marks/entry")
async def create_entry_mark(request: dict):
    """Create entry mark"""
    return {
        "success": True,
        "id": f"entry_{int(datetime.now().timestamp())}",
        "message": "Entry mark created successfully"
    }

@app.post("/api/marks/exit")
async def create_exit_mark(request: dict):
    """Create exit mark"""
    return {
        "success": True,
        "id": f"exit_{int(datetime.now().timestamp())}",
        "message": "Exit mark created successfully"
    }

# Strategy session endpoints
@app.post("/api/strategy/session/create")
async def create_strategy_session(request: dict):
    """Create new strategy session"""
    return {
        "success": True,
        "session_id": f"session_{int(datetime.now().timestamp())}",
        "message": "Strategy session created successfully"
    }

@app.get("/api/strategy/sessions")
async def get_strategy_sessions():
    """Get all strategy sessions"""
    return {
        "success": True,
        "sessions": [
            {
                "id": "session_1",
                "name": "Test Session",
                "created_at": datetime.now().isoformat(),
                "symbol": "BTCUSDT",
                "timeframe": "1h"
            }
        ]
    }

def main():
    """Main function to run the server"""
    try:
        logger.info("🚀 Starting Strategy Builder server (working version)...")
        logger.info("📍 Server will be available at: http://127.0.0.1:8001")
        logger.info("📖 API documentation: http://127.0.0.1:8001/docs")
        logger.info("🔧 This version includes all necessary API endpoints")
        
        # Run the server directly
        uvicorn.run(
            app,
            host="127.0.0.1",
            port=8001,
            reload=False,
            log_level="info"
        )
        
    except KeyboardInterrupt:
        logger.info("🛑 Server stopped by user")
    except Exception as e:
        logger.error(f"❌ Server error: {e}")
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
