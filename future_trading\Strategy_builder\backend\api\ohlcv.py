"""
OHLCV API endpoints
"""
from fastapi import APIRouter, HTTPException, Depends, Query
from sqlalchemy.orm import Session
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
from pydantic import BaseModel
import logging

from config.database import get_db
from backend.database.operations import OHLCVOperations
from backend.services.mexc_service import data_service
from config.settings import validate_symbol, validate_timeframe

logger = logging.getLogger(__name__)
router = APIRouter()


class OHLCVRequest(BaseModel):
    exchange: str = "binance"
    symbol: str
    timeframe: str
    limit: Optional[int] = 500
    start_date: Optional[str] = None
    end_date: Optional[str] = None
    force_refresh: Optional[bool] = False


class OHLCVResponse(BaseModel):
    symbol: str
    timeframe: str
    exchange: str
    data: List[Dict[str, Any]]
    count: int
    from_cache: bool


@router.get("/{exchange}/{symbol}/{timeframe}")
async def get_ohlcv_data(
    exchange: str,
    symbol: str,
    timeframe: str,
    limit: int = Query(default=500, ge=1, le=5000),
    start_date: Optional[str] = Query(default=None),
    end_date: Optional[str] = Query(default=None),
    force_refresh: bool = Query(default=False),
    db: Session = Depends(get_db)
) -> OHLCVResponse:
    """
    Get OHLCV data for a symbol and timeframe
    
    Args:
        exchange: Exchange name (binance, mexc)
        symbol: Trading symbol (e.g., BTCUSDT)
        timeframe: Timeframe (1m, 5m, 1h, 1d, etc.)
        limit: Number of candles to return
        start_date: Start date (ISO format)
        end_date: End date (ISO format)
        force_refresh: Force refresh from exchange API
        
    Returns:
        OHLCV data response
    """
    try:
        # Validate inputs
        if not validate_symbol(symbol):
            raise HTTPException(status_code=400, detail="Invalid symbol format")
        
        if not validate_timeframe(timeframe):
            raise HTTPException(status_code=400, detail="Invalid timeframe")
        
        if exchange.lower() not in ["binance", "mexc"]:
            raise HTTPException(status_code=400, detail="Unsupported exchange")
        
        # Parse dates
        start_time = None
        end_time = None
        
        if start_date:
            try:
                start_time = datetime.fromisoformat(start_date.replace('Z', '+00:00'))
            except ValueError:
                raise HTTPException(status_code=400, detail="Invalid start_date format")
        
        if end_date:
            try:
                end_time = datetime.fromisoformat(end_date.replace('Z', '+00:00'))
            except ValueError:
                raise HTTPException(status_code=400, detail="Invalid end_date format")
        
        # Check cache first (if not forcing refresh)
        from_cache = False
        if not force_refresh:
            cached_data = OHLCVOperations.get_ohlcv_data(
                db=db,
                symbol=symbol,
                timeframe=timeframe,
                start_time=start_time,
                end_time=end_time,
                limit=limit
            )
            
            if cached_data and len(cached_data) >= limit * 0.8:  # Use cache if we have 80% of requested data
                logger.info(f"Using cached data for {symbol} {timeframe}")
                from_cache = True
                return OHLCVResponse(
                    symbol=symbol,
                    timeframe=timeframe,
                    exchange=exchange,
                    data=cached_data[-limit:],  # Return last N candles
                    count=len(cached_data[-limit:]),
                    from_cache=from_cache
                )
        
        # Fetch from exchange API
        logger.info(f"Fetching fresh data from {exchange} for {symbol} {timeframe}")
        
        # Validate symbol on exchange
        if not data_service.validate_symbol(exchange, symbol):
            raise HTTPException(status_code=404, detail=f"Symbol {symbol} not found on {exchange}")
        
        # Fetch data
        ohlcv_data = data_service.get_klines(
            exchange=exchange,
            symbol=symbol,
            timeframe=timeframe,
            limit=limit,
            start_time=start_time,
            end_time=end_time
        )
        
        if not ohlcv_data:
            raise HTTPException(status_code=404, detail="No data found")
        
        # Store in database
        try:
            inserted_count = OHLCVOperations.insert_ohlcv_data(
                db=db,
                symbol=symbol,
                timeframe=timeframe,
                data=ohlcv_data
            )
            logger.info(f"Inserted {inserted_count} new candles into database")
        except Exception as e:
            logger.warning(f"Failed to store data in database: {e}")
            # Continue without storing
        
        # Convert to response format
        response_data = []
        for candle in ohlcv_data:
            response_data.append({
                'timestamp': candle['timestamp'].isoformat(),
                'open': candle['open'],
                'high': candle['high'],
                'low': candle['low'],
                'close': candle['close'],
                'volume': candle['volume']
            })
        
        return OHLCVResponse(
            symbol=symbol,
            timeframe=timeframe,
            exchange=exchange,
            data=response_data,
            count=len(response_data),
            from_cache=from_cache
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting OHLCV data: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/fetch")
async def fetch_ohlcv_data(
    request: OHLCVRequest,
    db: Session = Depends(get_db)
) -> OHLCVResponse:
    """
    Fetch OHLCV data using POST request
    """
    return await get_ohlcv_data(
        exchange=request.exchange,
        symbol=request.symbol,
        timeframe=request.timeframe,
        limit=request.limit or 500,
        start_date=request.start_date,
        end_date=request.end_date,
        force_refresh=request.force_refresh or False,
        db=db
    )


@router.get("/{exchange}/{symbol}/{timeframe}/latest")
async def get_latest_candle(
    exchange: str,
    symbol: str,
    timeframe: str,
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """Get the latest candle for a symbol"""
    try:
        # Get latest candle from database
        latest_data = OHLCVOperations.get_ohlcv_data(
            db=db,
            symbol=symbol,
            timeframe=timeframe,
            limit=1
        )
        
        if latest_data:
            return {
                "symbol": symbol,
                "timeframe": timeframe,
                "latest_candle": latest_data[-1],
                "from_cache": True
            }
        
        # If no cached data, fetch from exchange
        ohlcv_data = data_service.get_klines(
            exchange=exchange,
            symbol=symbol,
            timeframe=timeframe,
            limit=1
        )
        
        if not ohlcv_data:
            raise HTTPException(status_code=404, detail="No data found")
        
        latest_candle = ohlcv_data[-1]
        return {
            "symbol": symbol,
            "timeframe": timeframe,
            "latest_candle": {
                'timestamp': latest_candle['timestamp'].isoformat(),
                'open': latest_candle['open'],
                'high': latest_candle['high'],
                'low': latest_candle['low'],
                'close': latest_candle['close'],
                'volume': latest_candle['volume']
            },
            "from_cache": False
        }
        
    except Exception as e:
        logger.error(f"Error getting latest candle: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.delete("/{symbol}/{timeframe}/cache")
async def clear_cache(
    symbol: str,
    timeframe: str,
    db: Session = Depends(get_db)
) -> Dict[str, str]:
    """Clear cached OHLCV data for a symbol/timeframe"""
    try:
        # This would implement cache clearing logic
        # For now, just return success
        return {
            "message": f"Cache cleared for {symbol} {timeframe}",
            "status": "success"
        }
        
    except Exception as e:
        logger.error(f"Error clearing cache: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")
