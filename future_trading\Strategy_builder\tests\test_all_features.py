#!/usr/bin/env python3
"""
Comprehensive Test Script for Strategy Builder
Tests all features including database, API, indicators, and trading functionality
"""
import sys
import os
import asyncio
import json
from datetime import datetime, timedelta
import logging

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.settings import settings
from config.database import test_connection, health_check
from backend.database.operations_pymysql import (
    OHLCVOperations, IndicatorsOperations, ManualMarksOperations,
    StrategySessionsOperations
)
from backend.core.technical_indicators import technical_indicators, calculate_profit_pnl
from backend.services.binance_service import binance_service
from backend.services.mexc_service import mexc_service, data_service

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class StrategyBuilderTester:
    """Comprehensive tester for Strategy Builder features"""
    
    def __init__(self):
        self.test_results = {}
        self.session_id = None
        self.test_symbol = "BTCUSDT"
        self.test_timeframe = "1h"
        self.test_exchange = "binance"
        
    async def run_all_tests(self):
        """Run all test suites"""
        logger.info("🚀 Starting Strategy Builder Comprehensive Tests")
        logger.info("=" * 60)
        
        test_suites = [
            ("Database Connection", self.test_database_connection),
            ("Database Schema", self.test_database_schema),
            ("Exchange APIs", self.test_exchange_apis),
            ("OHLCV Operations", self.test_ohlcv_operations),
            ("Technical Indicators", self.test_technical_indicators),
            ("Session Management", self.test_session_management),
            ("Trade Marking", self.test_trade_marking),
            ("Strategy Logging", self.test_strategy_logging),
            ("Data Export", self.test_data_export),
            ("Performance", self.test_performance)
        ]
        
        for test_name, test_func in test_suites:
            try:
                logger.info(f"\n📋 Testing: {test_name}")
                logger.info("-" * 40)
                
                result = await test_func()
                self.test_results[test_name] = {
                    'status': 'PASSED' if result else 'FAILED',
                    'details': result if isinstance(result, dict) else {}
                }
                
                if result:
                    logger.info(f"✅ {test_name}: PASSED")
                else:
                    logger.error(f"❌ {test_name}: FAILED")
                    
            except Exception as e:
                logger.error(f"❌ {test_name}: ERROR - {e}")
                self.test_results[test_name] = {
                    'status': 'ERROR',
                    'error': str(e)
                }
        
        # Print summary
        self.print_test_summary()
        
    async def test_database_connection(self):
        """Test database connection and health"""
        try:
            # Test basic connection
            if not test_connection():
                return False
            
            # Test health check
            health = health_check()
            if health['status'] != 'healthy':
                return False
            
            logger.info(f"Database: {health['database']} {health['version']}")
            return True
            
        except Exception as e:
            logger.error(f"Database connection error: {e}")
            return False
    
    async def test_database_schema(self):
        """Test database schema and tables"""
        try:
            with get_db_session() as db:
                # Test each table exists and has data structure
                from backend.database.models import (
                    OHLCVData, IndicatorsData, ManualMarks, 
                    StrategyLog, StrategySessions
                )
                
                tables = [
                    ('ohlcv_data', OHLCVData),
                    ('indicators_data', IndicatorsData),
                    ('manual_marks', ManualMarks),
                    ('strategy_log', StrategyLog),
                    ('strategy_sessions', StrategySessions)
                ]
                
                for table_name, model_class in tables:
                    count = db.query(model_class).count()
                    logger.info(f"Table {table_name}: {count} records")
                
                return True
                
        except Exception as e:
            logger.error(f"Database schema error: {e}")
            return False
    
    async def test_exchange_apis(self):
        """Test exchange API connections"""
        try:
            results = {}
            
            # Test Binance
            try:
                binance_health = binance_service.test_connection()
                results['binance'] = binance_health
                logger.info(f"Binance API: {'✅ Connected' if binance_health else '❌ Failed'}")
            except Exception as e:
                results['binance'] = False
                logger.warning(f"Binance API error: {e}")
            
            # Test MEXC
            try:
                mexc_health = mexc_service.test_connection()
                results['mexc'] = mexc_health
                logger.info(f"MEXC API: {'✅ Connected' if mexc_health else '❌ Failed'}")
            except Exception as e:
                results['mexc'] = False
                logger.warning(f"MEXC API error: {e}")
            
            # Test unified data service
            try:
                unified_results = data_service.test_connections()
                results['unified'] = unified_results
                logger.info(f"Unified service: {unified_results}")
            except Exception as e:
                results['unified'] = False
                logger.warning(f"Unified service error: {e}")
            
            return results
            
        except Exception as e:
            logger.error(f"Exchange API test error: {e}")
            return False
    
    async def test_ohlcv_operations(self):
        """Test OHLCV data operations"""
        try:
            # Fetch sample data
            logger.info(f"Fetching OHLCV data for {self.test_symbol} {self.test_timeframe}")
            
            ohlcv_data = data_service.get_klines(
                exchange=self.test_exchange,
                symbol=self.test_symbol,
                timeframe=self.test_timeframe,
                limit=100
            )
            
            if not ohlcv_data:
                return False
            
            logger.info(f"Fetched {len(ohlcv_data)} candles")
            
            # Test database operations
            with get_db_session() as db:
                # Insert data
                inserted_count = OHLCVOperations.insert_ohlcv_data(
                    db=db,
                    symbol=self.test_symbol,
                    timeframe=self.test_timeframe,
                    data=ohlcv_data
                )
                
                logger.info(f"Inserted {inserted_count} new records")
                
                # Retrieve data
                retrieved_data = OHLCVOperations.get_ohlcv_data(
                    db=db,
                    symbol=self.test_symbol,
                    timeframe=self.test_timeframe,
                    limit=50
                )
                
                logger.info(f"Retrieved {len(retrieved_data)} records from database")
                
                return len(retrieved_data) > 0
                
        except Exception as e:
            logger.error(f"OHLCV operations error: {e}")
            return False
    
    async def test_technical_indicators(self):
        """Test technical indicators calculation"""
        try:
            # Get OHLCV data
            with get_db_session() as db:
                ohlcv_data = OHLCVOperations.get_ohlcv_data(
                    db=db,
                    symbol=self.test_symbol,
                    timeframe=self.test_timeframe,
                    limit=100
                )
            
            if not ohlcv_data:
                logger.warning("No OHLCV data available for indicator testing")
                return False
            
            # Test each indicator
            indicators_config = [
                {'name': 'RSI', 'params': {'length': 14}},
                {'name': 'MACD', 'params': {'fast': 12, 'slow': 26, 'signal': 9}},
                {'name': 'EMA', 'params': {'length': 20}},
                {'name': 'SMA', 'params': {'length': 20}},
                {'name': 'BB', 'params': {'length': 20, 'std': 2}},
                {'name': 'STOCH', 'params': {'k': 14, 'd': 3, 'smooth_k': 3}}
            ]
            
            results = {}
            
            for config in indicators_config:
                try:
                    indicator_data = technical_indicators.calculate_indicators(
                        ohlcv_data=ohlcv_data,
                        indicators_config=[config]
                    )
                    
                    indicator_name = config['name']
                    if indicator_name in indicator_data:
                        data_points = len(indicator_data[indicator_name])
                        valid_points = sum(1 for point in indicator_data[indicator_name] 
                                         if point['value'] and any(v is not None for v in point['value'].values()))
                        
                        results[indicator_name] = {
                            'total_points': data_points,
                            'valid_points': valid_points,
                            'success': valid_points > 0
                        }
                        
                        logger.info(f"{indicator_name}: {valid_points}/{data_points} valid points")
                        
                        # Store in database
                        with get_db_session() as db:
                            IndicatorOperations.insert_indicator_data(
                                db=db,
                                symbol=self.test_symbol,
                                timeframe=self.test_timeframe,
                                indicator_name=indicator_name,
                                indicator_params=config['params'],
                                data=indicator_data[indicator_name]
                            )
                    
                except Exception as e:
                    logger.error(f"Error calculating {config['name']}: {e}")
                    results[config['name']] = {'success': False, 'error': str(e)}
            
            return results
            
        except Exception as e:
            logger.error(f"Technical indicators error: {e}")
            return False
    
    async def test_session_management(self):
        """Test session management operations"""
        try:
            with get_db_session() as db:
                # Create test session
                session_id = SessionOperations.create_session(
                    db=db,
                    symbol=self.test_symbol,
                    timeframe=self.test_timeframe,
                    session_name=f"Test Session {datetime.now().strftime('%Y%m%d_%H%M%S')}"
                )
                
                self.session_id = session_id
                logger.info(f"Created test session: {session_id}")
                
                # Update session stats (will be empty initially)
                SessionOperations.update_session_stats(db, session_id)
                logger.info("Updated session statistics")
                
                return True
                
        except Exception as e:
            logger.error(f"Session management error: {e}")
            return False
    
    async def test_trade_marking(self):
        """Test trade marking operations"""
        try:
            if not self.session_id:
                logger.error("No session available for trade marking test")
                return False
            
            with get_db_session() as db:
                # Get sample OHLCV data for timestamps
                ohlcv_data = OHLCVOperations.get_ohlcv_data(
                    db=db,
                    symbol=self.test_symbol,
                    timeframe=self.test_timeframe,
                    limit=10
                )
                
                if len(ohlcv_data) < 2:
                    logger.error("Insufficient OHLCV data for trade marking test")
                    return False
                
                # Create entry mark
                entry_candle = ohlcv_data[0]
                entry_timestamp = datetime.fromisoformat(entry_candle['timestamp'].replace('Z', '+00:00'))
                
                # Get indicator snapshot
                indicator_snapshot = await self.get_test_indicator_snapshot(entry_timestamp)
                
                entry_id = MarkOperations.create_entry_mark(
                    db=db,
                    symbol=self.test_symbol,
                    timeframe=self.test_timeframe,
                    timestamp=entry_timestamp,
                    price=entry_candle['close'],
                    entry_side='buy',
                    indicator_snapshot=indicator_snapshot,
                    ohlcv_snapshot=entry_candle,
                    session_id=self.session_id
                )
                
                logger.info(f"Created entry mark: {entry_id}")
                
                # Create exit mark
                exit_candle = ohlcv_data[5]  # Use a later candle
                exit_timestamp = datetime.fromisoformat(exit_candle['timestamp'].replace('Z', '+00:00'))
                
                exit_indicator_snapshot = await self.get_test_indicator_snapshot(exit_timestamp)
                
                exit_id = MarkOperations.create_exit_mark(
                    db=db,
                    symbol=self.test_symbol,
                    timeframe=self.test_timeframe,
                    timestamp=exit_timestamp,
                    price=exit_candle['close'],
                    indicator_snapshot=exit_indicator_snapshot,
                    ohlcv_snapshot=exit_candle,
                    linked_trade_id=entry_id,
                    session_id=self.session_id
                )
                
                logger.info(f"Created exit mark: {exit_id}")
                
                # Test profit calculation
                profit_data = calculate_profit_pnl(
                    entry_price=entry_candle['close'],
                    exit_price=exit_candle['close'],
                    entry_side='buy'
                )
                
                logger.info(f"Calculated P&L: {profit_data['profit_pct']:.2f}%")
                
                return {
                    'entry_id': entry_id,
                    'exit_id': exit_id,
                    'profit_pct': profit_data['profit_pct']
                }
                
        except Exception as e:
            logger.error(f"Trade marking error: {e}")
            return False
    
    async def get_test_indicator_snapshot(self, timestamp):
        """Get indicator snapshot for testing"""
        try:
            with get_db_session() as db:
                # Try to get RSI data for the timestamp
                rsi_data = IndicatorOperations.get_indicator_data(
                    db=db,
                    symbol=self.test_symbol,
                    timeframe=self.test_timeframe,
                    indicator_name='RSI',
                    timestamp=timestamp
                )
                
                if rsi_data:
                    return {'RSI': rsi_data['value']}
                else:
                    return {'RSI': {'rsi': 50.0}}  # Default value
                    
        except Exception:
            return {'RSI': {'rsi': 50.0}}  # Default value
    
    async def test_strategy_logging(self):
        """Test strategy logging operations"""
        try:
            if not self.session_id:
                logger.error("No session available for strategy logging test")
                return False
            
            with get_db_session() as db:
                # Get marks from the session
                marks = MarkOperations.get_marks_by_session(db, self.session_id)
                
                entry_marks = [m for m in marks if m['mark_type'] == 'entry']
                exit_marks = [m for m in marks if m['mark_type'] == 'exit']
                
                if not entry_marks or not exit_marks:
                    logger.warning("No entry/exit marks available for strategy logging test")
                    return True  # Not a failure, just no data
                
                # Get the actual mark objects for strategy log creation
                from backend.database.models import ManualMarks
                
                entry_mark_obj = db.query(ManualMarks).filter(
                    ManualMarks.id == entry_marks[0]['id']
                ).first()
                
                exit_mark_obj = db.query(ManualMarks).filter(
                    ManualMarks.id == exit_marks[0]['id']
                ).first()
                
                if entry_mark_obj and exit_mark_obj:
                    # Calculate profit
                    profit_data = calculate_profit_pnl(
                        entry_price=float(entry_mark_obj.price),
                        exit_price=float(exit_mark_obj.price),
                        entry_side=entry_mark_obj.entry_side
                    )
                    
                    # Create strategy log
                    strategy_id = StrategyOperations.create_strategy_log(
                        db=db,
                        entry_mark=entry_mark_obj,
                        exit_mark=exit_mark_obj,
                        profit_pct=profit_data['profit_pct'],
                        profit_absolute=profit_data['profit_absolute']
                    )
                    
                    logger.info(f"Created strategy log: {strategy_id}")
                    
                    # Update session stats
                    SessionOperations.update_session_stats(db, self.session_id)
                    logger.info("Updated session statistics")
                    
                    return {'strategy_id': strategy_id}
                
                return True
                
        except Exception as e:
            logger.error(f"Strategy logging error: {e}")
            return False
    
    async def test_data_export(self):
        """Test data export functionality"""
        try:
            if not self.session_id:
                logger.error("No session available for data export test")
                return False
            
            with get_db_session() as db:
                # Test getting session data
                strategy_logs = StrategyOperations.get_strategy_logs_by_session(db, self.session_id)
                marks = MarkOperations.get_marks_by_session(db, self.session_id)
                
                logger.info(f"Session has {len(strategy_logs)} strategy logs and {len(marks)} marks")
                
                # Test export data structure
                export_data = {
                    'session_id': self.session_id,
                    'symbol': self.test_symbol,
                    'timeframe': self.test_timeframe,
                    'strategy_logs': strategy_logs,
                    'marks': marks,
                    'exported_at': datetime.now().isoformat()
                }
                
                # Test JSON serialization
                json_data = json.dumps(export_data, default=str, indent=2)
                logger.info(f"JSON export size: {len(json_data)} characters")
                
                return {
                    'strategy_logs_count': len(strategy_logs),
                    'marks_count': len(marks),
                    'json_size': len(json_data)
                }
                
        except Exception as e:
            logger.error(f"Data export error: {e}")
            return False
    
    async def test_performance(self):
        """Test performance with larger datasets"""
        try:
            start_time = datetime.now()
            
            # Test with larger OHLCV dataset
            ohlcv_data = data_service.get_klines(
                exchange=self.test_exchange,
                symbol=self.test_symbol,
                timeframe=self.test_timeframe,
                limit=1000
            )
            
            fetch_time = (datetime.now() - start_time).total_seconds()
            logger.info(f"Fetched 1000 candles in {fetch_time:.2f} seconds")
            
            # Test indicator calculation performance
            start_time = datetime.now()
            
            indicators_config = [
                {'name': 'RSI', 'params': {'length': 14}},
                {'name': 'MACD', 'params': {'fast': 12, 'slow': 26, 'signal': 9}},
                {'name': 'EMA', 'params': {'length': 20}}
            ]
            
            indicator_data = technical_indicators.calculate_indicators(
                ohlcv_data=ohlcv_data,
                indicators_config=indicators_config
            )
            
            calc_time = (datetime.now() - start_time).total_seconds()
            logger.info(f"Calculated 3 indicators on 1000 candles in {calc_time:.2f} seconds")
            
            return {
                'fetch_time': fetch_time,
                'calculation_time': calc_time,
                'candles_processed': len(ohlcv_data),
                'indicators_calculated': len(indicator_data)
            }
            
        except Exception as e:
            logger.error(f"Performance test error: {e}")
            return False
    
    def print_test_summary(self):
        """Print comprehensive test summary"""
        logger.info("\n" + "=" * 60)
        logger.info("🏁 STRATEGY BUILDER TEST SUMMARY")
        logger.info("=" * 60)
        
        passed = sum(1 for result in self.test_results.values() if result['status'] == 'PASSED')
        failed = sum(1 for result in self.test_results.values() if result['status'] == 'FAILED')
        errors = sum(1 for result in self.test_results.values() if result['status'] == 'ERROR')
        total = len(self.test_results)
        
        logger.info(f"Total Tests: {total}")
        logger.info(f"✅ Passed: {passed}")
        logger.info(f"❌ Failed: {failed}")
        logger.info(f"🔥 Errors: {errors}")
        logger.info(f"Success Rate: {(passed/total)*100:.1f}%")
        
        logger.info("\nDetailed Results:")
        logger.info("-" * 40)
        
        for test_name, result in self.test_results.items():
            status_icon = "✅" if result['status'] == 'PASSED' else "❌" if result['status'] == 'FAILED' else "🔥"
            logger.info(f"{status_icon} {test_name}: {result['status']}")
            
            if 'error' in result:
                logger.info(f"   Error: {result['error']}")
            elif 'details' in result and result['details']:
                logger.info(f"   Details: {result['details']}")
        
        logger.info("\n" + "=" * 60)
        
        if passed == total:
            logger.info("🎉 ALL TESTS PASSED! Strategy Builder is ready for use!")
        elif passed > total * 0.8:
            logger.info("⚠️  Most tests passed. Check failed tests before production use.")
        else:
            logger.info("🚨 Multiple test failures. Review and fix issues before use.")


async def main():
    """Main test function"""
    tester = StrategyBuilderTester()
    await tester.run_all_tests()


if __name__ == "__main__":
    asyncio.run(main())
