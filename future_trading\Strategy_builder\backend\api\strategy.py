"""
Strategy API endpoints for session management and data export
"""
from fastapi import APIRouter, HTTPException, Depends, Response
from fastapi.responses import StreamingResponse
from sqlalchemy.orm import Session
from typing import List, Dict, Any, Optional
from pydantic import BaseModel
import json
import csv
import io
from datetime import datetime
import logging

from config.database import get_db
from backend.database.operations import SessionOperations, StrategyOperations, MarkOperations

logger = logging.getLogger(__name__)
router = APIRouter()


class SessionRequest(BaseModel):
    symbol: str
    timeframe: str
    session_name: Optional[str] = None


class SessionResponse(BaseModel):
    session_id: str
    session_name: str
    symbol: str
    timeframe: str
    created_at: str


@router.post("/session/create")
async def create_session(
    request: SessionRequest,
    db: Session = Depends(get_db)
) -> SessionResponse:
    """Create a new strategy session"""
    try:
        session_id = SessionOperations.create_session(
            db=db,
            symbol=request.symbol,
            timeframe=request.timeframe,
            session_name=request.session_name
        )
        
        # Get the created session details
        from backend.database.models import StrategySessions
        session = db.query(StrategySessions).filter(
            StrategySessions.session_id == session_id
        ).first()
        
        if not session:
            raise HTTPException(status_code=500, detail="Failed to create session")
        
        return SessionResponse(
            session_id=session.session_id,
            session_name=session.session_name,
            symbol=session.symbol,
            timeframe=session.timeframe,
            created_at=session.created_at.isoformat()
        )
        
    except Exception as e:
        logger.error(f"Error creating session: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/session/{session_id}")
async def get_session(
    session_id: str,
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """Get session details and statistics"""
    try:
        from backend.database.models import StrategySessions
        
        session = db.query(StrategySessions).filter(
            StrategySessions.session_id == session_id
        ).first()
        
        if not session:
            raise HTTPException(status_code=404, detail="Session not found")
        
        # Get strategy logs for this session
        strategy_logs = StrategyOperations.get_strategy_logs_by_session(db, session_id)
        
        # Get marks for this session
        marks = MarkOperations.get_marks_by_session(db, session_id)
        
        return {
            "session": session.to_dict(),
            "strategy_logs": strategy_logs,
            "marks": marks,
            "statistics": {
                "total_trades": len(strategy_logs),
                "total_marks": len(marks),
                "win_rate": float(session.win_rate),
                "total_profit_pct": float(session.total_profit_pct),
                "avg_profit_per_trade": float(session.avg_profit_per_trade)
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting session: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/sessions")
async def list_sessions(
    symbol: Optional[str] = None,
    timeframe: Optional[str] = None,
    limit: int = 50,
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """List strategy sessions with optional filtering"""
    try:
        from backend.database.models import StrategySessions
        from sqlalchemy import desc
        
        query = db.query(StrategySessions)
        
        if symbol:
            query = query.filter(StrategySessions.symbol == symbol)
        if timeframe:
            query = query.filter(StrategySessions.timeframe == timeframe)
        
        sessions = query.order_by(desc(StrategySessions.created_at)).limit(limit).all()
        
        return {
            "sessions": [session.to_dict() for session in sessions],
            "count": len(sessions)
        }
        
    except Exception as e:
        logger.error(f"Error listing sessions: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/export/{session_id}/json")
async def export_session_json(
    session_id: str,
    db: Session = Depends(get_db)
) -> StreamingResponse:
    """Export session data as JSON"""
    try:
        # Get session data
        session_data = await get_session(session_id, db)
        
        # Create JSON string
        json_data = json.dumps(session_data, indent=2, default=str)
        
        # Create streaming response
        def generate():
            yield json_data
        
        filename = f"strategy_session_{session_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        return StreamingResponse(
            io.StringIO(json_data),
            media_type="application/json",
            headers={"Content-Disposition": f"attachment; filename={filename}"}
        )
        
    except Exception as e:
        logger.error(f"Error exporting session JSON: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/export/{session_id}/csv")
async def export_session_csv(
    session_id: str,
    db: Session = Depends(get_db)
) -> StreamingResponse:
    """Export session trades as CSV"""
    try:
        # Get strategy logs
        strategy_logs = StrategyOperations.get_strategy_logs_by_session(db, session_id)
        
        if not strategy_logs:
            raise HTTPException(status_code=404, detail="No trades found for session")
        
        # Create CSV content
        output = io.StringIO()
        writer = csv.writer(output)
        
        # Write header
        writer.writerow([
            'Strategy ID', 'Symbol', 'Timeframe', 'Entry Side', 'Entry Price', 'Exit Price',
            'Profit %', 'Profit Absolute', 'Entry Time', 'Exit Time', 'Duration (min)',
            'Entry RSI', 'Entry MACD', 'Exit RSI', 'Exit MACD'
        ])
        
        # Write data rows
        for log in strategy_logs:
            entry_indicators = log.get('entry_indicator_snapshot', {})
            exit_indicators = log.get('exit_indicator_snapshot', {})
            
            writer.writerow([
                log['strategy_id'],
                log['symbol'],
                log['timeframe'],
                log['entry_side'],
                log['entry_price'],
                log['exit_price'],
                log['profit_pct'],
                log['profit_absolute'],
                log['entry_timestamp'],
                log['exit_timestamp'],
                log['trade_duration_minutes'],
                entry_indicators.get('RSI', {}).get('rsi', ''),
                entry_indicators.get('MACD', {}).get('macd', ''),
                exit_indicators.get('RSI', {}).get('rsi', ''),
                exit_indicators.get('MACD', {}).get('macd', '')
            ])
        
        output.seek(0)
        
        filename = f"strategy_trades_{session_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        
        return StreamingResponse(
            io.StringIO(output.getvalue()),
            media_type="text/csv",
            headers={"Content-Disposition": f"attachment; filename={filename}"}
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error exporting session CSV: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/analytics/{session_id}")
async def get_session_analytics(
    session_id: str,
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """Get detailed analytics for a session"""
    try:
        # Get strategy logs
        strategy_logs = StrategyOperations.get_strategy_logs_by_session(db, session_id)
        
        if not strategy_logs:
            return {
                "session_id": session_id,
                "analytics": {},
                "message": "No trades found"
            }
        
        # Calculate analytics
        total_trades = len(strategy_logs)
        winning_trades = [log for log in strategy_logs if log['profit_pct'] > 0]
        losing_trades = [log for log in strategy_logs if log['profit_pct'] <= 0]
        
        win_rate = (len(winning_trades) / total_trades) * 100 if total_trades > 0 else 0
        
        profits = [log['profit_pct'] for log in strategy_logs]
        total_profit = sum(profits)
        avg_profit = total_profit / total_trades if total_trades > 0 else 0
        
        max_profit = max(profits) if profits else 0
        max_loss = min(profits) if profits else 0
        
        # Calculate consecutive wins/losses
        consecutive_wins = 0
        consecutive_losses = 0
        max_consecutive_wins = 0
        max_consecutive_losses = 0
        
        for log in strategy_logs:
            if log['profit_pct'] > 0:
                consecutive_wins += 1
                consecutive_losses = 0
                max_consecutive_wins = max(max_consecutive_wins, consecutive_wins)
            else:
                consecutive_losses += 1
                consecutive_wins = 0
                max_consecutive_losses = max(max_consecutive_losses, consecutive_losses)
        
        return {
            "session_id": session_id,
            "analytics": {
                "total_trades": total_trades,
                "winning_trades": len(winning_trades),
                "losing_trades": len(losing_trades),
                "win_rate": round(win_rate, 2),
                "total_profit_pct": round(total_profit, 2),
                "avg_profit_per_trade": round(avg_profit, 2),
                "max_profit": round(max_profit, 2),
                "max_loss": round(max_loss, 2),
                "max_consecutive_wins": max_consecutive_wins,
                "max_consecutive_losses": max_consecutive_losses,
                "profit_factor": round(
                    sum([p for p in profits if p > 0]) / abs(sum([p for p in profits if p < 0])) 
                    if any(p < 0 for p in profits) else float('inf'), 2
                )
            }
        }
        
    except Exception as e:
        logger.error(f"Error getting session analytics: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.delete("/session/{session_id}")
async def delete_session(
    session_id: str,
    db: Session = Depends(get_db)
) -> Dict[str, str]:
    """Delete a strategy session and all associated data"""
    try:
        # This would implement session deletion logic
        # For now, return a placeholder
        return {
            "message": f"Session {session_id} deletion not yet implemented",
            "status": "pending"
        }
        
    except Exception as e:
        logger.error(f"Error deleting session: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")
