"""
Strategy API endpoints for session management and data export
Now using PyMySQL for direct database operations
"""
from fastapi import APIRouter, HTTPException, Response
from fastapi.responses import StreamingResponse
from typing import List, Dict, Any, Optional
from pydantic import BaseModel
import json
import csv
import io
from datetime import datetime
import logging

from backend.database.operations_pymysql import StrategySessionsOperations, ManualMarksOperations

logger = logging.getLogger(__name__)
router = APIRouter()


class SessionRequest(BaseModel):
    symbol: str
    timeframe: str
    session_name: Optional[str] = None


class SessionResponse(BaseModel):
    session_id: str
    session_name: str
    symbol: str
    timeframe: str
    created_at: str


@router.post("/session/create")
async def create_session(request: SessionRequest) -> SessionResponse:
    """Create a new strategy session"""
    try:
        session_id = StrategySessionsOperations.create_new_session(
            symbol=request.symbol,
            timeframe=request.timeframe,
            session_name=request.session_name
        )
        
        # Get the created session details
        sessions = StrategySessionsOperations.get_active_sessions(request.symbol, request.timeframe)
        session = next((s for s in sessions if s['session_id'] == session_id), None)
        
        if not session:
            raise HTTPException(status_code=500, detail="Failed to create session")
        
        return SessionResponse(
            session_id=session['session_id'],
            session_name=session['session_name'],
            symbol=session['symbol'],
            timeframe=session['timeframe'],
            created_at=session['created_at']
        )
        
    except Exception as e:
        logger.error(f"Error creating session: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/session/{session_id}")
async def get_session(session_id: str) -> Dict[str, Any]:
    """Get session details and statistics"""
    try:
        # Get all sessions and find the one we want
        all_sessions = StrategySessionsOperations.get_active_sessions()
        session = next((s for s in all_sessions if s['session_id'] == session_id), None)
        
        if not session:
            raise HTTPException(status_code=404, detail="Session not found")
        
        # Get marks for this session
        marks = ManualMarksOperations.get_marks_for_chart(
            session['symbol'], 
            session['timeframe'], 
            session_id
        )
        
        # Calculate basic statistics
        entry_marks = [m for m in marks if m['mark_type'] == 'entry']
        exit_marks = [m for m in marks if m['mark_type'] == 'exit']
        
        return {
            "session": session,
            "marks": marks,
            "statistics": {
                "total_marks": len(marks),
                "entry_marks": len(entry_marks),
                "exit_marks": len(exit_marks),
                "win_rate": session.get('win_rate', 0),
                "total_profit_pct": session.get('total_profit_pct', 0),
                "total_trades": session.get('total_trades', 0)
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting session: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/sessions")
async def list_sessions(
    symbol: Optional[str] = None,
    timeframe: Optional[str] = None,
    limit: int = 50
) -> Dict[str, Any]:
    """List strategy sessions with optional filtering"""
    try:
        sessions = StrategySessionsOperations.get_active_sessions(symbol, timeframe)
        
        # Apply limit
        if limit and len(sessions) > limit:
            sessions = sessions[:limit]
        
        return {
            "sessions": sessions,
            "count": len(sessions)
        }
        
    except Exception as e:
        logger.error(f"Error listing sessions: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/export/{session_id}/json")
async def export_session_json(session_id: str) -> StreamingResponse:
    """Export session data as JSON"""
    try:
        # Get session data
        session_data = await get_session(session_id)
        
        # Create JSON string
        json_data = json.dumps(session_data, indent=2, default=str)
        
        filename = f"strategy_session_{session_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        return StreamingResponse(
            io.StringIO(json_data),
            media_type="application/json",
            headers={"Content-Disposition": f"attachment; filename={filename}"}
        )
        
    except Exception as e:
        logger.error(f"Error exporting session JSON: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/export/{session_id}/csv")
async def export_session_csv(session_id: str) -> StreamingResponse:
    """Export session marks as CSV"""
    try:
        # Get session data
        session_data = await get_session(session_id)
        marks = session_data.get('marks', [])
        
        # Create CSV
        output = io.StringIO()
        writer = csv.writer(output)
        
        # Write header
        writer.writerow([
            'timestamp', 'price', 'mark_type', 'entry_side', 
            'linked_trade_id', 'created_at'
        ])
        
        # Write data
        for mark in marks:
            writer.writerow([
                mark.get('timestamp', ''),
                mark.get('price', ''),
                mark.get('mark_type', ''),
                mark.get('entry_side', ''),
                mark.get('linked_trade_id', ''),
                datetime.now().isoformat()
            ])
        
        output.seek(0)
        filename = f"strategy_marks_{session_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        
        return StreamingResponse(
            io.StringIO(output.getvalue()),
            media_type="text/csv",
            headers={"Content-Disposition": f"attachment; filename={filename}"}
        )
        
    except Exception as e:
        logger.error(f"Error exporting session CSV: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/analytics/{session_id}")
async def get_session_analytics(session_id: str) -> Dict[str, Any]:
    """Get detailed analytics for a session"""
    try:
        # Get session data
        session_data = await get_session(session_id)
        marks = session_data.get('marks', [])
        
        # Basic analytics
        entry_marks = [m for m in marks if m['mark_type'] == 'entry']
        exit_marks = [m for m in marks if m['mark_type'] == 'exit']
        
        # Calculate time-based statistics
        if marks:
            timestamps = [m['timestamp'] for m in marks if m.get('timestamp')]
            if timestamps:
                first_mark = min(timestamps)
                last_mark = max(timestamps)
                duration_hours = (last_mark - first_mark) / (1000 * 60 * 60)  # Convert ms to hours
            else:
                duration_hours = 0
        else:
            duration_hours = 0
        
        analytics = {
            "session_id": session_id,
            "total_marks": len(marks),
            "entry_marks": len(entry_marks),
            "exit_marks": len(exit_marks),
            "session_duration_hours": round(duration_hours, 2),
            "marks_per_hour": round(len(marks) / max(duration_hours, 1), 2),
            "entry_exit_ratio": round(len(entry_marks) / max(len(exit_marks), 1), 2),
            "session_info": session_data.get('session', {}),
            "statistics": session_data.get('statistics', {})
        }
        
        return analytics
        
    except Exception as e:
        logger.error(f"Error getting session analytics: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.delete("/session/{session_id}")
async def delete_session(session_id: str) -> Dict[str, str]:
    """Delete a strategy session and all associated data"""
    try:
        # For now, return a placeholder since we need to implement deletion
        # This would require adding delete methods to the operations classes
        return {
            "message": f"Session deletion not yet implemented for session {session_id}",
            "status": "placeholder"
        }
        
    except Exception as e:
        logger.error(f"Error deleting session: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/health")
async def strategy_health() -> Dict[str, str]:
    """Strategy API health check"""
    return {
        "status": "healthy",
        "service": "strategy_api",
        "timestamp": datetime.now().isoformat()
    }
