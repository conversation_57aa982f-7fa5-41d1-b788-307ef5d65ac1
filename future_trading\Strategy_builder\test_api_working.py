#!/usr/bin/env python3
"""
Simplified API Test Script for Strategy Builder
Tests the working API endpoints and validates key functionality
"""
import requests
import json
import time
from datetime import datetime
import sys

class StrategyBuilderAPITester:
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        self.test_results = []
        
    def log_test(self, test_name: str, success: bool, details: str = ""):
        """Log test result"""
        status = "✅ PASS" if success else "❌ FAIL"
        result = {
            "test": test_name,
            "status": status,
            "success": success,
            "details": details,
            "timestamp": datetime.now().isoformat()
        }
        self.test_results.append(result)
        print(f"{status} {test_name}")
        if details:
            print(f"    {details}")
        
    def test_core_endpoints(self):
        """Test core working endpoints"""
        print("\n🔍 Testing Core API Endpoints...")
        
        # Test /api/health endpoint
        try:
            response = self.session.get(f"{self.base_url}/api/health", timeout=5)
            if response.status_code == 200:
                data = response.json()
                self.log_test("GET /api/health", True, 
                            f"Service: {data.get('service')}, DB: {data.get('database')}")
            else:
                self.log_test("GET /api/health", False, f"Status code: {response.status_code}")
        except Exception as e:
            self.log_test("GET /api/health", False, f"Error: {str(e)}")
            
        # Test /api/config endpoint
        try:
            response = self.session.get(f"{self.base_url}/api/config", timeout=5)
            if response.status_code == 200:
                data = response.json()
                bb_config = data.get('default_indicator_params', {}).get('BB', {})
                bb_length = bb_config.get('length', 'N/A')
                indicators = data.get('supported_indicators', [])
                self.log_test("GET /api/config", True, 
                            f"BB length: {bb_length}, Total indicators: {len(indicators)}")
                
                # Validate Bollinger Bands configuration
                if bb_length == 4:
                    self.log_test("Bollinger Bands Length Validation", True, 
                                "BB default length is correctly set to 4")
                else:
                    self.log_test("Bollinger Bands Length Validation", False, 
                                f"Expected length 4, got {bb_length}")
                    
            else:
                self.log_test("GET /api/config", False, f"Status code: {response.status_code}")
        except Exception as e:
            self.log_test("GET /api/config", False, f"Error: {str(e)}")
            
        # Test /api/exchanges endpoint
        try:
            response = self.session.get(f"{self.base_url}/api/exchanges", timeout=5)
            if response.status_code == 200:
                data = response.json()
                exchanges = data.get('exchanges', [])
                self.log_test("GET /api/exchanges", True, f"Exchanges: {', '.join(exchanges)}")
            else:
                self.log_test("GET /api/exchanges", False, f"Status code: {response.status_code}")
        except Exception as e:
            self.log_test("GET /api/exchanges", False, f"Error: {str(e)}")
    
    def test_symbols_endpoints(self):
        """Test symbols endpoints"""
        print("\n🔍 Testing Symbols Endpoints...")
        
        for exchange in ["binance", "mexc"]:
            try:
                response = self.session.get(f"{self.base_url}/api/symbols/{exchange}", timeout=5)
                
                if response.status_code == 200:
                    data = response.json()
                    symbols = data.get('symbols', [])
                    self.log_test(f"GET /api/symbols/{exchange}", True, f"Found {len(symbols)} symbols")
                else:
                    self.log_test(f"GET /api/symbols/{exchange}", False, f"Status code: {response.status_code}")
            except Exception as e:
                self.log_test(f"GET /api/symbols/{exchange}", False, f"Error: {str(e)}")
    
    def test_frontend_integration(self):
        """Test frontend integration"""
        print("\n🔍 Testing Frontend Integration...")
        
        # Test main page
        try:
            response = self.session.get(f"{self.base_url}/", timeout=5)
            if response.status_code == 200:
                content = response.text
                if "Strategy Builder" in content:
                    self.log_test("GET / (Frontend)", True, "Frontend page loads successfully")
                else:
                    self.log_test("GET / (Frontend)", False, "Frontend content not found")
            else:
                self.log_test("GET / (Frontend)", False, f"Status code: {response.status_code}")
        except Exception as e:
            self.log_test("GET / (Frontend)", False, f"Error: {str(e)}")
        
        # Test API documentation
        try:
            response = self.session.get(f"{self.base_url}/docs", timeout=5)
            if response.status_code == 200:
                self.log_test("GET /docs (API Docs)", True, "API documentation accessible")
            else:
                self.log_test("GET /docs (API Docs)", False, f"Status code: {response.status_code}")
        except Exception as e:
            self.log_test("GET /docs (API Docs)", False, f"Error: {str(e)}")
    
    def validate_bollinger_bands_config(self):
        """Validate Bollinger Bands configuration specifically"""
        print("\n🎯 Validating Bollinger Bands Configuration...")
        
        try:
            response = self.session.get(f"{self.base_url}/api/config", timeout=5)
            if response.status_code == 200:
                data = response.json()
                
                # Check backend configuration
                bb_params = data.get('default_indicator_params', {}).get('BB', {})
                length = bb_params.get('length')
                std = bb_params.get('std')
                
                if length == 4 and std == 2:
                    self.log_test("BB Backend Configuration", True, 
                                f"✅ Backend: length={length}, std={std}")
                else:
                    self.log_test("BB Backend Configuration", False, 
                                f"❌ Backend: length={length}, std={std} (expected: length=4, std=2)")
                
                # Check if BB is in supported indicators
                supported = data.get('supported_indicators', [])
                if 'BB' in supported:
                    self.log_test("BB Support Check", True, "✅ BB is in supported indicators")
                else:
                    self.log_test("BB Support Check", False, "❌ BB not found in supported indicators")
                    
            else:
                self.log_test("BB Configuration Check", False, f"Config endpoint failed: {response.status_code}")
        except Exception as e:
            self.log_test("BB Configuration Check", False, f"Error: {str(e)}")
    
    def run_working_tests(self):
        """Run tests for working endpoints only"""
        print("🚀 Starting Strategy Builder API Tests (Working Endpoints Only)...")
        print(f"📍 Testing server at: {self.base_url}")
        print("=" * 70)
        
        # Run test categories
        self.test_core_endpoints()
        self.test_symbols_endpoints()
        self.test_frontend_integration()
        self.validate_bollinger_bands_config()
        
        # Print summary
        self.print_summary()
    
    def print_summary(self):
        """Print test summary"""
        print("\n" + "=" * 70)
        print("📊 TEST SUMMARY - WORKING ENDPOINTS")
        print("=" * 70)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result['success'])
        failed_tests = total_tests - passed_tests
        
        print(f"Total Tests: {total_tests}")
        print(f"✅ Passed: {passed_tests}")
        print(f"❌ Failed: {failed_tests}")
        print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        if failed_tests > 0:
            print(f"\n❌ Failed Tests:")
            for result in self.test_results:
                if not result['success']:
                    print(f"  - {result['test']}: {result['details']}")
        
        print("\n🎯 Key Validation Results:")
        
        # Check specific validations
        bb_length_test = next((r for r in self.test_results if 'BB' in r['test'] and 'length' in r['details'].lower()), None)
        if bb_length_test and bb_length_test['success']:
            print("  ✅ Bollinger Bands default length is correctly set to 4")
        
        health_test = next((r for r in self.test_results if 'api/health' in r['test']), None)
        if health_test and health_test['success']:
            print("  ✅ API health endpoint is working properly")
        
        config_test = next((r for r in self.test_results if 'api/config' in r['test']), None)
        if config_test and config_test['success']:
            print("  ✅ Configuration endpoint is working properly")
            
        frontend_test = next((r for r in self.test_results if 'Frontend' in r['test']), None)
        if frontend_test and frontend_test['success']:
            print("  ✅ Frontend is accessible and loading")
        
        print(f"\n📝 Detailed results saved to test_results_working.json")
        
        # Save detailed results to file
        with open('test_results_working.json', 'w') as f:
            json.dump(self.test_results, f, indent=2)
        
        print("\n🎉 CONCLUSION:")
        if passed_tests >= total_tests * 0.8:  # 80% success rate
            print("✅ Strategy Builder API is working well!")
            print("✅ Bollinger Bands configuration is correct!")
            print("✅ Core functionality is operational!")
        else:
            print("⚠️  Some issues detected, but core functionality appears to work")

def main():
    """Main function"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Test Strategy Builder API (Working Endpoints)')
    parser.add_argument('--url', default='http://localhost:8000', 
                       help='Base URL of the API server (default: http://localhost:8000)')
    
    args = parser.parse_args()
    
    # Create tester and run tests
    tester = StrategyBuilderAPITester(args.url)
    
    try:
        tester.run_working_tests()
    except KeyboardInterrupt:
        print("\n\n⚠️ Tests interrupted by user")
        tester.print_summary()
    except Exception as e:
        print(f"\n\n❌ Test execution failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
